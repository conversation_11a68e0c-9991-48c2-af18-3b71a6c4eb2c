/* Custom styles for Bug Reporter App */

/* General Styles */
body {
    transition: background-color 0.5s ease, color 0.5s ease;
    position: relative;
}

.card {
    transition: all 0.5s ease;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #1a1a2e;
    color: #f0f0f0;
    background-image: linear-gradient(to bottom right, #1a1a2e, #16213e);
    background-attachment: fixed;
}

body.dark-mode .card {
    background-color: #16213e;
    color: #f0f0f0;
    border-color: #0f3460;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

body.dark-mode .card-header {
    background-color: #0f3460 !important;
    border-bottom: 1px solid #1a1a2e;
}

body.dark-mode .card-header.bg-primary {
    background-color: #1e56a0 !important;
}

body.dark-mode .card-header.bg-secondary {
    background-color: #2d4263 !important;
}

body.dark-mode .table {
    color: #f0f0f0;
    border-color: #0f3460;
}

body.dark-mode .table thead th {
    border-bottom-color: #0f3460;
    background-color: #0f3460;
    color: #d6e4f0;
}

body.dark-mode .table td {
    border-color: #0f3460;
}

body.dark-mode .modal-content {
    background-color: #16213e;
    color: #f0f0f0;
    border: 1px solid #0f3460;
}

body.dark-mode .modal-header,
body.dark-mode .modal-footer {
    border-color: #333;
}

body.dark-mode .form-control,
body.dark-mode .form-select {
    background-color: #1a1a2e;
    color: #f0f0f0;
    border-color: #0f3460;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background-color: #1a1a2e;
    border-color: #4cc9f0;
    box-shadow: 0 0 0 0.25rem rgba(76, 201, 240, 0.25);
}

/* Animation Classes */
.bug-item-new {
    animation: highlightNew 1.5s ease;
}

.bug-item-delete {
    animation: fadeOut 0.5s ease forwards;
}

/* Form animations */
.highlight-field {
    animation: pulseField 1s ease;
}

#generateBtn:active {
    animation: btnPulse 0.5s ease;
}

#summarizeBenefitsBtn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

#summarizeBenefitsBtn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

#summarizeBenefitsBtn:hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 1.5s infinite;
}

/* Keyframes */
@keyframes highlightNew {
    0% {
        background-color: rgba(40, 167, 69, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(50px);
    }
}

@keyframes pulseField {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.5);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

@keyframes btnPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.95);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Dark Mode Highlight Animation */
@keyframes highlightNewDark {
    0% {
        background-color: rgba(40, 167, 69, 0.2);
    }
    100% {
        background-color: transparent;
    }
}

body.dark-mode .bug-item-new {
    animation: highlightNewDark 1.5s ease;
}

@keyframes fadeOut {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(50px);
    }
}

/* Dark Mode Bug Details */
body.dark-mode .bug-details h3,
body.dark-mode .bug-details h5 {
    color: #4cc9f0;
}

body.dark-mode .bug-title-link {
    color: #4cc9f0;
    text-decoration: none;
}

body.dark-mode .bug-title-link:hover {
    color: #72efdd;
    text-decoration: underline;
}

body.dark-mode tr:hover {
    background-color: #0f3460;
}

body.dark-mode .close,
body.dark-mode .btn-close {
    color: #e0e0e0;
    text-shadow: none;
}

body.dark-mode .modal-header .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Severity Badge Styles */
.badge-low {
    background-color: #28a745;
}

.badge-medium {
    background-color: #ffc107;
    color: #212529;
}

.badge-high {
    background-color: #fd7e14;
}

.badge-critical {
    background-color: #dc3545;
}

body.dark-mode .badge-low {
    background-color: #2a9d8f;
}

body.dark-mode .badge-medium {
    background-color: #e9c46a;
    color: #16213e;
}

body.dark-mode .badge-high {
    background-color: #f4a261;
}

body.dark-mode .badge-critical {
    background-color: #e63946;
}

/* Dark Mode Button Styles */
body.dark-mode .btn-primary {
    background-color: #1e56a0;
    border-color: #1e56a0;
}

body.dark-mode .btn-secondary {
    background-color: #2d4263;
    border-color: #2d4263;
}

body.dark-mode .btn-success {
    background-color: #2a9d8f;
    border-color: #2a9d8f;
}

body.dark-mode .btn-info {
    background-color: #4cc9f0;
    border-color: #4cc9f0;
    color: #16213e;
}

body.dark-mode .btn-outline-primary {
    color: #4cc9f0;
    border-color: #4cc9f0;
}

body.dark-mode .btn-outline-primary:hover {
    background-color: #1e56a0;
    border-color: #1e56a0;
    color: #fff;
}

body.dark-mode .btn-outline-danger {
    color: #e63946;
    border-color: #e63946;
}

body.dark-mode .btn-outline-danger:hover {
    background-color: #e63946;
    border-color: #e63946;
    color: #fff;
}

/* Dark Mode Toast Styles */
body.dark-mode .toast {
    background-color: #16213e;
    border-color: #0f3460;
}

body.dark-mode .toast-header {
    background-color: #0f3460;
    color: #f0f0f0;
    border-color: #1a1a2e;
}

/* Dark Mode Form Switch */
body.dark-mode .form-check-input {
    background-color: #1a1a2e;
    border-color: #0f3460;
}

body.dark-mode .form-check-input:checked {
    background-color: #4cc9f0;
    border-color: #4cc9f0;
}

/* Settings Modal Styles */
.form-text {
    font-size: 0.8rem;
    opacity: 0.8;
}

body.dark-mode .form-text {
    color: #a0a0a0;
}

/* API Key Toggle Button */
#toggleApiKey:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

body.dark-mode #toggleApiKey {
    color: #f0f0f0;
    border-color: #0f3460;
    background-color: #1a1a2e;
}

body.dark-mode #toggleApiKey:hover {
    background-color: #0f3460;
}

/* Settings Button */
.btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}

/* Dark Mode Text Styles */
body.dark-mode .text-muted {
    color: #d0d0d0 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .card-header h3 {
        font-size: 1.2rem;
    }
}

/* Heart Animation */
@keyframes heartbeat {
  0% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

footer .text-danger {
  display: inline-block;
  animation: heartbeat 1.5s infinite;
}

footer {
  position: relative;
  z-index: 1000;
  margin-top: 50px !important;
  display: block !important;
  visibility: visible !important;
}

footer .container {
  display: block !important;
  visibility: visible !important;
}

/* Footer Centering Styles */
footer .container {
  text-align: center;
  background: transparent;
}

footer .mb-1 {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  font-size: 1.2rem;
  letter-spacing: 1px;
}

footer .text-danger {
  font-size: 1.4rem;
}

footer .rounded {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
  max-width: 200px;
  background: transparent;
  border: none;
}

body.dark-mode footer {
  color: #f0f0f0;
}

body.dark-mode footer .mb-1.fw-bold {
  color: #ffffff;
}

body.dark-mode footer .text-muted {
  color: #d0d0d0 !important;
}

body.dark-mode footer a {
  color: #4cc9f0;
}

body.dark-mode footer a:hover {
  color: #72efdd;
}

body.dark-mode footer .rounded {
  background: linear-gradient(to right, #16213e, #1a1a2e) !important;
  border: 1px solid #0f3460;
}

/* Responsive Footer Styles */
@media (max-width: 576px) {
  footer .mb-1.fw-bold {
    font-size: 0.9rem;
  }

  footer .text-muted.small {
    font-size: 0.75rem;
  }
}

@media (max-width: 400px) {
  footer .mb-1.fw-bold {
    font-size: 0.85rem;
  }
}

/* ===== SMART AI FEATURES STYLES ===== */

/* Quality Score Card */
.quality-score-card {
    border: 2px solid #17a2b8;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s ease;
}

.quality-score-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.15);
}

body.dark-mode .quality-score-card {
    background: linear-gradient(135deg, #2d4263 0%, #1a1a2e 100%);
    border-color: #17a2b8;
}

/* Progress Bar Animations */
.progress-bar {
    transition: width 0.6s ease, background-color 0.3s ease;
}

/* Alert Styles for Suggestions */
.alert-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Category Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.8em;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* Smart Suggestion Buttons */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Dark Mode Alert Styles */
body.dark-mode .alert-info {
    background-color: rgba(23, 162, 184, 0.15);
    border-color: #17a2b8;
    color: #b8daff;
}

body.dark-mode .alert-warning {
    background-color: rgba(255, 193, 7, 0.15);
    border-color: #ffc107;
    color: #fff3cd;
}

body.dark-mode .alert-success {
    background-color: rgba(40, 167, 69, 0.15);
    border-color: #28a745;
    color: #d4edda;
}

body.dark-mode .alert-danger {
    background-color: rgba(220, 53, 69, 0.15);
    border-color: #dc3545;
    color: #f8d7da;
}

/* Form Field Enhancements */
.form-control:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

body.dark-mode .form-control {
    background-color: #2d4263;
    border-color: #0f3460;
    color: #f0f0f0;
}

body.dark-mode .form-control:focus {
    background-color: #2d4263;
    border-color: #17a2b8;
    color: #f0f0f0;
}

/* Duplicate Warning Pulse Animation */
.alert-warning {
    animation: pulseWarning 2s infinite;
}

@keyframes pulseWarning {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

/* Success Suggestion Glow */
.alert-success {
    animation: glowSuccess 1.5s ease-in-out;
}

@keyframes glowSuccess {
    0% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.3); }
    50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.6); }
    100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.3); }
}

/* Quality Score Animations */
.quality-excellent {
    animation: celebrateExcellent 1s ease-in-out;
}

@keyframes celebrateExcellent {
    0%, 100% { transform: scale(1); }
    25% { transform: scale(1.05); }
    50% { transform: scale(1.02); }
    75% { transform: scale(1.05); }
}

/* Responsive Adjustments for Smart Features */
@media (max-width: 768px) {
    .alert-sm {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.3em 0.6em;
    }

    .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* ===== ADVANCED FEATURES STYLES ===== */

/* Smart Toolbar */
.smart-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

body.dark-mode .smart-toolbar {
    background: linear-gradient(135deg, #2d4263 0%, #1a1a2e 100%);
}

/* Template Cards */
.template-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.template-card:hover {
    transform: translateY(-5px);
    border-color: #007bff;
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

/* Voice and transcript styles removed */

/* File Upload Area */
.evidence-upload-area {
    transition: all 0.3s ease;
    border: 2px dashed #dee2e6;
    background: #f8f9fa;
}

.evidence-upload-area:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.evidence-upload-area.drag-over {
    border-color: #28a745;
    background: #d4edda;
    transform: scale(1.02);
}

body.dark-mode .evidence-upload-area {
    background: #2d4263;
    border-color: #495057;
}

body.dark-mode .evidence-upload-area:hover {
    background: #1e3a5f;
    border-color: #17a2b8;
}

/* Uploaded File Items */
.uploaded-file-item {
    transition: all 0.3s ease;
    animation: slideInUp 0.3s ease;
}

.uploaded-file-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Analytics Charts */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Quality Score Indicators */
.quality-excellent {
    color: #28a745 !important;
    font-weight: bold;
}

.quality-good {
    color: #ffc107 !important;
    font-weight: bold;
}

.quality-poor {
    color: #dc3545 !important;
    font-weight: bold;
}

/* Environment Info */
.environment-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
}

body.dark-mode .environment-info {
    background: linear-gradient(135deg, #1e3a5f 0%, #2d4263 100%);
}

/* AI Insights Panel */
.ai-insights-panel {
    border: 2px solid #17a2b8;
    border-radius: 12px;
    background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
    animation: fadeInUp 0.5s ease;
}

body.dark-mode .ai-insights-panel {
    background: linear-gradient(135deg, #1e3a5f 0%, #2d4263 100%);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Bug List */
.bug-title-link {
    text-decoration: none;
    color: inherit;
    font-weight: 500;
}

.bug-title-link:hover {
    color: #007bff;
    text-decoration: underline;
}

/* Modal Enhancements */
.modal-xl {
    max-width: 95%;
}

.modal-content {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Button Group Enhancements */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

/* Screenshot canvas styles removed */

/* Integration Buttons */
.integration-btn {
    transition: all 0.3s ease;
    border-radius: 10px;
}

.integration-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Analytics Summary Cards */
.analytics-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 10px 0;
}

/* Search and Filter Bar */
.search-filter-bar {
    background: rgba(0, 123, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
}

body.dark-mode .search-filter-bar {
    background: rgba(23, 162, 184, 0.1);
}

/* Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Tooltips */
[title] {
    position: relative;
}

/* Integration Configuration Styles */
.integration-item {
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    transition: all 0.3s ease;
}

.integration-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

body.dark-mode .integration-item {
    border-color: #495057;
    background: #2d4263;
}

body.dark-mode .integration-item:hover {
    border-color: #17a2b8;
}

.integration-config {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e9ecef;
    animation: slideDown 0.3s ease;
}

body.dark-mode .integration-config {
    background: #1a1a2e;
    border-color: #495057;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        padding: 0 10px;
    }
    to {
        opacity: 1;
        max-height: 200px;
        padding: 10px;
    }
}

.integration-config input,
.integration-config select {
    transition: all 0.2s ease;
}

.integration-config input:focus,
.integration-config select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Connection Status Indicators */
.connection-status {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 8px;
}

.connection-status.connected {
    background-color: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
}

.connection-status.disconnected {
    background-color: #dc3545;
}

.connection-status.testing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

/* Export Button Enhancements */
.export-btn-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.export-btn-group .btn {
    justify-content: flex-start;
    text-align: left;
}

/* Copy Button Enhancements */
.copy-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.copy-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.copy-btn:active {
    transform: translateY(0);
}

.copy-btn.btn-success {
    animation: copySuccess 0.3s ease;
}

@keyframes copySuccess {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Manual Copy Modal Styling */
#manualCopyModal .modal-content {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#manualCopyModal .modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0;
}

#manualCopyModal .btn-close {
    filter: invert(1);
}

#manualCopyText {
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    resize: none;
}

#manualCopyText:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* Dark mode styles for copy functionality */
body.dark-mode #manualCopyModal .modal-content {
    background-color: #16213e;
    color: #f0f0f0;
}

body.dark-mode #manualCopyText {
    background-color: #1a1a2e;
    color: #f0f0f0;
    border-color: #0f3460;
}

body.dark-mode #manualCopyText:focus {
    border-color: #4cc9f0;
    box-shadow: 0 0 0 0.25rem rgba(76, 201, 240, 0.25);
}

/* Mobile Optimizations for Advanced Features */
@media (max-width: 768px) {
    .smart-toolbar .btn {
        margin: 2px;
        font-size: 0.8rem;
    }

    .modal-xl {
        max-width: 98%;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin: 1px 0;
    }

    .evidence-upload-area {
        padding: 15px 10px;
    }

    .analytics-summary {
        padding: 15px;
        margin: 5px 0;
    }

    .integration-item {
        margin-bottom: 10px;
        padding: 8px;
    }

    .integration-config {
        padding: 8px;
    }

    /* Mobile copy button adjustments */
    .copy-btn {
        min-width: 38px;
        padding: 0.375rem 0.5rem;
    }

    #manualCopyModal .modal-dialog {
        margin: 1rem;
    }
}
