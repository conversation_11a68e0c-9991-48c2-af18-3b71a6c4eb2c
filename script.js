// Bug Reporter App - Main JavaScript

// Initialize AOS animations
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS animation library
    AOS.init({
        duration: 800,
        easing: 'ease',
        once: true
    });


    // Always enable dark mode
    document.body.classList.add('dark-mode');
    localStorage.setItem('darkMode', 'enabled');

    // Load API key and settings from localStorage
    loadSettings();

    // Load bugs from localStorage
    loadBugs();

    // Initialize generate button
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateBugDetails);
    }

    // Initialize AI enhance button for test case generation
    const aiEnhanceBtn = document.getElementById('aiEnhanceBtn');
    if (aiEnhanceBtn) {
        aiEnhanceBtn.addEventListener('click', generateAdvancedTestCases);
    }

    // Initialize settings related elements
    initializeSettings();

    // Initialize Smart AI Features
    initializeSmartFeatures();

    // Initialize Advanced Features
    initializeAdvancedFeatures();

    // Initialize Environment Detection
    detectEnvironment();

    // Initialize Analytics
    updateAnalytics();

    // Initialize Install Extension Button
    initializeInstallExtension();

    // Initial call to summarizeBug if there's existing data in the form
    if (titleInput.value || descriptionInput.value || severityInput.value) {
        summarizeBug();
    }

    // Initialize Copy Buttons
    initializeCopyButtons();
});

// Initialize Copy Buttons
function initializeCopyButtons() {
    const copyButtons = document.querySelectorAll('.copy-btn:not([data-initialized])');
    copyButtons.forEach(button => {
        button.setAttribute('data-initialized', 'true');
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const targetId = this.dataset.target;
            const targetElement = document.getElementById(targetId);

            if (!targetElement) {
                showToast('Target element not found', 'error');
                return;
            }

            let textToCopy = '';

            // Get text based on element type
            if (targetElement.tagName === 'INPUT' || targetElement.tagName === 'TEXTAREA') {
                textToCopy = targetElement.value;
            } else if (targetElement.tagName === 'SELECT') {
                const selectedOption = targetElement.options[targetElement.selectedIndex];
                textToCopy = selectedOption ? selectedOption.text : '';
            } else {
                textToCopy = targetElement.textContent || targetElement.innerText;
            }

            // Clean up the text
            textToCopy = textToCopy.trim();

            if (!textToCopy) {
                showToast('Nothing to copy', 'warning');
                return;
            }

            // Try to copy using modern clipboard API first
            copyToClipboard(textToCopy, this);
        });
    });
}

// Enhanced clipboard copy function with fallback
function copyToClipboard(text, buttonElement) {
    // Check if modern clipboard API is available
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text)
            .then(() => {
                showCopySuccess(buttonElement);
                showToast('Copied to clipboard!', 'success');
            })
            .catch(err => {
                console.warn('Modern clipboard API failed, trying fallback:', err);
                fallbackCopyToClipboard(text, buttonElement);
            });
    } else {
        // Use fallback for older browsers or non-secure contexts
        fallbackCopyToClipboard(text, buttonElement);
    }
}

// Fallback copy method for older browsers
function fallbackCopyToClipboard(text, buttonElement) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);

        // Select and copy the text
        textArea.focus();
        textArea.select();

        // Use modern approach with fallback
        let successful = false;
        try {
            successful = document.execCommand('copy');
        } catch (execError) {
            console.warn('execCommand failed, trying alternative approach:', execError);
            successful = false;
        }
        document.body.removeChild(textArea);

        if (successful) {
            showCopySuccess(buttonElement);
            showToast('Copied to clipboard!', 'success');
        } else {
            throw new Error('Copy command failed');
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        showToast('Failed to copy. Please copy manually.', 'error');

        // Show the text in a modal for manual copying
        showTextForManualCopy(text);
    }
}

// Show copy success animation
function showCopySuccess(buttonElement) {
    if (!buttonElement) return;

    const originalIcon = buttonElement.innerHTML;
    buttonElement.innerHTML = '<i class="bi bi-check-lg text-success"></i>';
    buttonElement.classList.add('btn-success');
    buttonElement.classList.remove('btn-outline-secondary');

    setTimeout(() => {
        buttonElement.innerHTML = originalIcon;
        buttonElement.classList.remove('btn-success');
        buttonElement.classList.add('btn-outline-secondary');
    }, 1500);
}

// Show text in modal for manual copying
function showTextForManualCopy(text) {
    const modalHtml = `
        <div class="modal fade" id="manualCopyModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Copy Text Manually</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Please select and copy the text below:</p>
                        <textarea class="form-control" rows="5" readonly id="manualCopyText">${text}</textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="selectAllText()">Select All</button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('manualCopyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add new modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('manualCopyModal'));
    modal.show();

    // Auto-select text when modal is shown
    document.getElementById('manualCopyModal').addEventListener('shown.bs.modal', function() {
        const textArea = document.getElementById('manualCopyText');
        textArea.select();
        textArea.focus();
    });
}

// Helper function to select all text in manual copy modal
function selectAllText() {
    const textArea = document.getElementById('manualCopyText');
    if (textArea) {
        textArea.select();
        textArea.focus();
    }
}

// ===== MULTI-API MANAGEMENT SYSTEM =====

// Multi-API Manager for intelligent API switching and load balancing
class MultiAPIManager {
    constructor() {
        this.apiKeys = this.loadAPIKeys();
        this.apiStats = this.loadAPIStats();
        this.currentAPIIndex = 0;
        this.rateLimitCooldowns = new Map();
        this.initializeAPIs();
    }

    // Load API keys from localStorage
    loadAPIKeys() {
        const defaultKeys = {
            'deepseek-r1': '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',
            'gemma-3b': '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',
            'deepseek-v2': '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',
            'qwen3-14b': '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'
        };

        const stored = localStorage.getItem('multiApiKeys');
        return stored ? { ...defaultKeys, ...JSON.parse(stored) } : defaultKeys;
    }

    // Load API statistics for intelligent selection
    loadAPIStats() {
        const defaultStats = {
            'deepseek-r1': { successRate: 0.9, avgResponseTime: 1200, lastUsed: 0, errorCount: 0, totalRequests: 0 },
            'gemma-3b': { successRate: 0.85, avgResponseTime: 800, lastUsed: 0, errorCount: 0, totalRequests: 0 },
            'deepseek-v2': { successRate: 0.88, avgResponseTime: 1000, lastUsed: 0, errorCount: 0, totalRequests: 0 },
            'qwen3-14b': { successRate: 0.92, avgResponseTime: 1500, lastUsed: 0, errorCount: 0, totalRequests: 0 }
        };

        const stored = localStorage.getItem('apiStats');
        return stored ? { ...defaultStats, ...JSON.parse(stored) } : defaultStats;
    }

    // Initialize API configurations
    initializeAPIs() {
        this.apiConfigs = {
            'deepseek-r1': {
                name: 'DeepSeek R1 0528',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1:nitro',
                strengths: ['complex-analysis', 'bug-analysis', 'reasoning'],
                maxTokens: 4000,
                temperature: 0.7
            },
            'gemma-3b': {
                name: 'Google Gemma 3B',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'google/gemma-2-9b-it:free',
                strengths: ['fast-response', 'simple-tasks', 'categorization'],
                maxTokens: 2000,
                temperature: 0.5
            },
            'deepseek-v2': {
                name: 'DeepSeek V2',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-v2.5',
                strengths: ['test-generation', 'detailed-analysis', 'code-understanding'],
                maxTokens: 3500,
                temperature: 0.6
            },
            'qwen3-14b': {
                name: 'Qwen3 14B',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'qwen/qwen-2.5-14b-instruct',
                strengths: ['comprehensive-testing', 'edge-cases', 'quality-analysis'],
                maxTokens: 4000,
                temperature: 0.8
            }
        };

        console.log('🚀 Multi-API Manager initialized with', Object.keys(this.apiKeys).length, 'AI models');
        this.updateAPIStatus();
    }

    // Select the best API for a specific task
    selectBestAPI(taskType = 'general') {
        const availableAPIs = Object.keys(this.apiKeys).filter(apiId =>
            !this.rateLimitCooldowns.has(apiId) ||
            Date.now() > this.rateLimitCooldowns.get(apiId)
        );

        if (availableAPIs.length === 0) {
            console.warn('All APIs are rate limited, using fallback');
            return Object.keys(this.apiKeys)[0];
        }

        // Task-specific API selection
        const taskPreferences = {
            'bug-analysis': ['deepseek-r1', 'deepseek-v2', 'qwen3-14b', 'gemma-3b'],
            'test-generation': ['qwen3-14b', 'deepseek-v2', 'deepseek-r1', 'gemma-3b'],
            'categorization': ['gemma-3b', 'deepseek-v2', 'qwen3-14b', 'deepseek-r1'],
            'severity-prediction': ['deepseek-r1', 'qwen3-14b', 'deepseek-v2', 'gemma-3b'],
            'general': ['deepseek-r1', 'qwen3-14b', 'deepseek-v2', 'gemma-3b']
        };

        const preferences = taskPreferences[taskType] || taskPreferences['general'];

        // Find the best available API based on preferences and performance
        for (const preferredAPI of preferences) {
            if (availableAPIs.includes(preferredAPI)) {
                const stats = this.apiStats[preferredAPI];
                if (stats.successRate > 0.7) {
                    return preferredAPI;
                }
            }
        }

        // Fallback to best performing available API
        return availableAPIs.reduce((best, current) => {
            const bestStats = this.apiStats[best];
            const currentStats = this.apiStats[current];

            const bestScore = bestStats.successRate * 0.7 + (1 / bestStats.avgResponseTime) * 0.3;
            const currentScore = currentStats.successRate * 0.7 + (1 / currentStats.avgResponseTime) * 0.3;

            return currentScore > bestScore ? current : best;
        });
    }

    // Enhanced API request with caching, timeouts, and progress tracking
    async makeAPIRequest(prompt, taskType = 'general', options = {}) {
        const requestId = options.requestId || 'ai-request';

        // Check cache first
        const cached = aiCache.get(prompt, taskType);
        if (cached) {
            return {
                result: cached.result,
                apiUsed: cached.apiUsed,
                responseTime: cached.responseTime,
                attempt: 1,
                fromCache: true
            };
        }

        const maxRetries = 3;
        let lastError = null;
        let progressInterval = null;

        // Create abort controller for cancellation
        const controller = cancellationManager.createController(requestId);

        try {
            for (let attempt = 0; attempt < maxRetries; attempt++) {
                const selectedAPI = this.selectBestAPI(taskType);
                const startTime = Date.now();

                // Start progress tracking
                if (attempt === 0) {
                    progressInterval = progressTracker.startProgress(requestId, taskType, selectedAPI);
                } else {
                    progressTracker.updateAttempt(requestId, attempt + 1);
                }

                try {
                    console.log(`🤖 Attempt ${attempt + 1}: Using ${this.apiConfigs[selectedAPI].name} for ${taskType}`);

                    const result = await this.callAPIWithTimeout(selectedAPI, prompt, {
                        ...options,
                        signal: controller.signal,
                        timeout: 30000 // 30 second timeout
                    });

                    const responseTime = Date.now() - startTime;

                    // Update success statistics
                    this.updateAPIStats(selectedAPI, true, responseTime);

                    // Cache successful result
                    const response = {
                        result,
                        apiUsed: selectedAPI,
                        responseTime,
                        attempt: attempt + 1
                    };
                    aiCache.set(prompt, taskType, response);

                    // Complete progress tracking
                    progressTracker.completeProgress(requestId, true);

                    return response;

                } catch (error) {
                    const responseTime = Date.now() - startTime;

                    console.warn(`❌ ${this.apiConfigs[selectedAPI].name} failed:`, error.message);

                    // Update failure statistics
                    this.updateAPIStats(selectedAPI, false, responseTime);

                    // Handle different error types
                    if (error.name === 'AbortError') {
                        throw new Error('Request was cancelled by user');
                    }

                    if (error.message.includes('rate limit') || error.message.includes('429')) {
                        this.rateLimitCooldowns.set(selectedAPI, Date.now() + 60000); // 1 minute cooldown
                        showToast(`${this.apiConfigs[selectedAPI].name} rate limited. Trying alternative...`, 'warning');
                    } else if (error.message.includes('timeout')) {
                        showToast(`${this.apiConfigs[selectedAPI].name} timed out. Retrying...`, 'warning');
                    }

                    lastError = error;

                    // If this was the last attempt, break
                    if (attempt === maxRetries - 1) break;

                    // Wait before retry with exponential backoff
                    await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt)));
                }
            }

            // All APIs failed
            progressTracker.completeProgress(requestId, false);
            throw new Error(`All API attempts failed. Last error: ${lastError?.message || 'Unknown error'}`);

        } finally {
            // Cleanup
            cancellationManager.cleanupRequest(requestId);
            if (progressInterval) {
                clearInterval(progressInterval);
            }
        }
    }

    // Enhanced API call with timeout support
    async callAPIWithTimeout(apiId, prompt, options = {}) {
        const config = this.apiConfigs[apiId];
        const apiKey = this.apiKeys[apiId];
        const timeout = options.timeout || 30000;

        if (!apiKey) {
            throw new Error(`API key not found for ${apiId}`);
        }

        const requestBody = {
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software testing and bug analysis AI assistant. Provide detailed, accurate, and actionable responses.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: options.maxTokens || config.maxTokens,
            temperature: options.temperature || config.temperature,
            stream: false
        };

        // Create timeout promise
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), timeout);
        });

        // Create fetch promise
        const fetchPromise = fetch(config.endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'HTTP-Referer': window.location.origin,
                'X-Title': 'Bug Reporter AI Assistant'
            },
            body: JSON.stringify(requestBody),
            signal: options.signal
        });

        // Race between fetch and timeout
        const response = await Promise.race([fetchPromise, timeoutPromise]);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API request failed: ${response.status} - ${errorText}`);
        }

        const data = await response.json();

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('Invalid API response format');
        }

        return data.choices[0].message.content;
    }

    // Call specific API
    async callAPI(apiId, prompt, options = {}) {
        const config = this.apiConfigs[apiId];
        const apiKey = this.apiKeys[apiId];

        if (!apiKey) {
            throw new Error(`API key not found for ${apiId}`);
        }

        const requestBody = {
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: 'You are an expert software testing and bug analysis AI assistant. Provide detailed, accurate, and actionable responses.'
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: options.maxTokens || config.maxTokens,
            temperature: options.temperature || config.temperature,
            stream: false
        };

        const response = await fetch(config.endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'HTTP-Referer': window.location.origin,
                'X-Title': 'Bug Reporter AI Assistant'
            },
            body: JSON.stringify(requestBody)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API request failed: ${response.status} - ${errorText}`);
        }

        const data = await response.json();

        if (!data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('Invalid API response format');
        }

        return data.choices[0].message.content;
    }

    // Update API statistics
    updateAPIStats(apiId, success, responseTime) {
        const stats = this.apiStats[apiId];
        stats.totalRequests++;
        stats.lastUsed = Date.now();

        if (success) {
            stats.successRate = (stats.successRate * (stats.totalRequests - 1) + 1) / stats.totalRequests;
            stats.avgResponseTime = (stats.avgResponseTime * (stats.totalRequests - 1) + responseTime) / stats.totalRequests;
        } else {
            stats.errorCount++;
            stats.successRate = (stats.successRate * (stats.totalRequests - 1)) / stats.totalRequests;
        }

        // Save to localStorage
        localStorage.setItem('apiStats', JSON.stringify(this.apiStats));
        this.updateAPIStatus();
    }

    // Update API status in UI
    updateAPIStatus() {
        const statusElement = document.getElementById('apiStatus');
        if (statusElement) {
            const activeAPIs = Object.keys(this.apiKeys).filter(apiId =>
                !this.rateLimitCooldowns.has(apiId) ||
                Date.now() > this.rateLimitCooldowns.get(apiId)
            );

            statusElement.innerHTML = `
                <div class="d-flex align-items-center gap-2">
                    <i class="bi bi-robot text-success"></i>
                    <span class="badge bg-success">${activeAPIs.length}/${Object.keys(this.apiKeys).length} APIs Active</span>
                </div>
            `;
        }
    }

    // Get API health status
    getAPIHealth() {
        return Object.entries(this.apiStats).map(([apiId, stats]) => ({
            id: apiId,
            name: this.apiConfigs[apiId].name,
            successRate: Math.round(stats.successRate * 100),
            avgResponseTime: Math.round(stats.avgResponseTime),
            isActive: !this.rateLimitCooldowns.has(apiId) || Date.now() > this.rateLimitCooldowns.get(apiId),
            totalRequests: stats.totalRequests,
            errorCount: stats.errorCount
        }));
    }
}

// Initialize Multi-API Manager
const multiAPIManager = new MultiAPIManager();

// ===== PERFORMANCE OPTIMIZATION SYSTEM =====

// Request cache for identical requests within session
class AIRequestCache {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 50;
        this.cacheTimeout = 300000; // 5 minutes
    }

    generateKey(prompt, taskType) {
        return btoa(prompt + '|' + taskType).substring(0, 32);
    }

    get(prompt, taskType) {
        const key = this.generateKey(prompt, taskType);
        const cached = this.cache.get(key);

        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            console.log('🎯 Using cached AI response');
            return cached.data;
        }

        if (cached) {
            this.cache.delete(key);
        }

        return null;
    }

    set(prompt, taskType, data) {
        const key = this.generateKey(prompt, taskType);

        // Implement LRU cache
        if (this.cache.size >= this.maxCacheSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    clear() {
        this.cache.clear();
    }
}

// Enhanced progress tracking system
class AIProgressTracker {
    constructor() {
        this.activeRequests = new Map();
        this.estimatedTimes = {
            'deepseek-r1': 1200,
            'gemma-3b': 800,
            'deepseek-v2': 1000,
            'qwen3-14b': 1500
        };
    }

    startProgress(requestId, taskType, apiId) {
        const startTime = Date.now();
        const estimatedDuration = this.estimatedTimes[apiId] || 1000;

        this.activeRequests.set(requestId, {
            taskType,
            apiId,
            startTime,
            estimatedDuration,
            attempt: 1,
            maxAttempts: 3
        });

        this.updateProgressUI(requestId);
        return this.startProgressInterval(requestId);
    }

    updateAttempt(requestId, attempt) {
        const request = this.activeRequests.get(requestId);
        if (request) {
            request.attempt = attempt;
            request.startTime = Date.now(); // Reset timer for new attempt
            this.updateProgressUI(requestId);
        }
    }

    startProgressInterval(requestId) {
        const interval = setInterval(() => {
            const request = this.activeRequests.get(requestId);
            if (!request) {
                clearInterval(interval);
                return;
            }

            const elapsed = Date.now() - request.startTime;
            const progress = Math.min((elapsed / request.estimatedDuration) * 100, 95);

            this.updateProgressBar(requestId, progress);

            if (elapsed > request.estimatedDuration * 2) {
                clearInterval(interval);
            }
        }, 100);

        return interval;
    }

    updateProgressUI(requestId) {
        const request = this.activeRequests.get(requestId);
        if (!request) return;

        const button = document.getElementById(requestId);
        if (!button) return;

        const apiName = multiAPIManager.apiConfigs[request.apiId]?.name || 'AI';
        const remainingTime = Math.max(0, request.estimatedDuration - (Date.now() - request.startTime));
        const remainingSeconds = Math.ceil(remainingTime / 1000);

        button.innerHTML = `
            <div class="d-flex align-items-center gap-2">
                <div class="spinner-border spinner-border-sm" role="status"></div>
                <div class="progress-info">
                    <div class="small">${apiName} (${request.attempt}/${request.maxAttempts})</div>
                    <div class="tiny text-muted">${remainingSeconds}s remaining</div>
                </div>
            </div>
        `;
    }

    updateProgressBar(requestId, progress) {
        const button = document.getElementById(requestId);
        if (!button) return;

        let progressBar = button.querySelector('.ai-progress-bar');
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'ai-progress-bar';
            progressBar.innerHTML = '<div class="ai-progress-fill"></div>';
            button.appendChild(progressBar);
        }

        const fill = progressBar.querySelector('.ai-progress-fill');
        if (fill) {
            fill.style.width = `${progress}%`;
        }
    }

    completeProgress(requestId, success = true) {
        const request = this.activeRequests.get(requestId);
        if (request) {
            const actualDuration = Date.now() - request.startTime;
            this.updateEstimatedTime(request.apiId, actualDuration);
        }

        this.activeRequests.delete(requestId);

        const button = document.getElementById(requestId);
        if (button) {
            const progressBar = button.querySelector('.ai-progress-bar');
            if (progressBar) {
                progressBar.remove();
            }
        }
    }

    updateEstimatedTime(apiId, actualDuration) {
        const current = this.estimatedTimes[apiId] || 1000;
        // Use exponential moving average
        this.estimatedTimes[apiId] = Math.round(current * 0.7 + actualDuration * 0.3);
    }

    cancelProgress(requestId) {
        this.activeRequests.delete(requestId);

        const button = document.getElementById(requestId);
        if (button) {
            const progressBar = button.querySelector('.ai-progress-bar');
            if (progressBar) {
                progressBar.remove();
            }
        }
    }
}

// Request cancellation system
class RequestCancellationManager {
    constructor() {
        this.activeControllers = new Map();
    }

    createController(requestId) {
        // Cancel any existing request with same ID
        this.cancelRequest(requestId);

        const controller = new AbortController();
        this.activeControllers.set(requestId, controller);
        return controller;
    }

    cancelRequest(requestId) {
        const controller = this.activeControllers.get(requestId);
        if (controller) {
            controller.abort();
            this.activeControllers.delete(requestId);
            console.log(`🚫 Cancelled request: ${requestId}`);
        }
    }

    cleanupRequest(requestId) {
        this.activeControllers.delete(requestId);
    }

    cancelAllRequests() {
        for (const [requestId, controller] of this.activeControllers) {
            controller.abort();
        }
        this.activeControllers.clear();
        console.log('🚫 Cancelled all active requests');
    }
}

// Initialize performance optimization systems
const aiCache = new AIRequestCache();
const progressTracker = new AIProgressTracker();
const cancellationManager = new RequestCancellationManager();

// ===== ENHANCED FEATURES SYSTEM =====

// AI Model Comparison System
class AIModelComparison {
    constructor() {
        this.comparisonResults = new Map();
    }

    async compareModels(prompt, taskType = 'general') {
        const availableModels = Object.keys(multiAPIManager.apiKeys);
        const results = [];

        console.log(`🔍 Comparing ${availableModels.length} AI models for ${taskType}`);

        // Run requests in parallel for faster comparison
        const promises = availableModels.map(async (modelId) => {
            try {
                const startTime = Date.now();
                const result = await multiAPIManager.callAPIWithTimeout(modelId, prompt, {
                    timeout: 20000 // Shorter timeout for comparison
                });

                return {
                    modelId,
                    modelName: multiAPIManager.apiConfigs[modelId].name,
                    result,
                    responseTime: Date.now() - startTime,
                    success: true,
                    error: null
                };
            } catch (error) {
                return {
                    modelId,
                    modelName: multiAPIManager.apiConfigs[modelId].name,
                    result: null,
                    responseTime: 0,
                    success: false,
                    error: error.message
                };
            }
        });

        const responses = await Promise.allSettled(promises);

        responses.forEach((response, index) => {
            if (response.status === 'fulfilled') {
                results.push(response.value);
            } else {
                results.push({
                    modelId: availableModels[index],
                    modelName: multiAPIManager.apiConfigs[availableModels[index]].name,
                    result: null,
                    responseTime: 0,
                    success: false,
                    error: response.reason?.message || 'Unknown error'
                });
            }
        });

        // Store comparison results
        const comparisonId = Date.now().toString();
        this.comparisonResults.set(comparisonId, {
            prompt,
            taskType,
            results,
            timestamp: new Date().toISOString()
        });

        return {
            comparisonId,
            results: results.sort((a, b) => a.responseTime - b.responseTime) // Sort by speed
        };
    }

    displayComparison(comparisonId) {
        const comparison = this.comparisonResults.get(comparisonId);
        if (!comparison) return;

        const modalHtml = `
            <div class="modal fade" id="aiComparisonModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-graph-up"></i> AI Model Comparison Results
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>Task Type:</strong> ${comparison.taskType}
                                </div>
                                <div class="col-md-6">
                                    <strong>Comparison Time:</strong> ${new Date(comparison.timestamp).toLocaleString()}
                                </div>
                            </div>

                            <div class="row">
                                ${comparison.results.map((result, index) => `
                                    <div class="col-md-6 mb-3">
                                        <div class="card ${result.success ? 'border-success' : 'border-danger'}">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">${result.modelName}</h6>
                                                <div class="d-flex gap-2">
                                                    ${result.success ?
                                                        `<span class="badge bg-success">${result.responseTime}ms</span>` :
                                                        `<span class="badge bg-danger">Failed</span>`
                                                    }
                                                    ${index === 0 && result.success ? '<span class="badge bg-warning">Fastest</span>' : ''}
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                ${result.success ?
                                                    `<div class="comparison-result" style="max-height: 200px; overflow-y: auto;">
                                                        ${result.result.substring(0, 500)}${result.result.length > 500 ? '...' : ''}
                                                    </div>
                                                    <div class="mt-2">
                                                        <button class="btn btn-sm btn-outline-primary" onclick="selectModelResult('${result.modelId}', '${comparisonId}')">
                                                            <i class="bi bi-check-circle"></i> Use This Result
                                                        </button>
                                                    </div>` :
                                                    `<div class="text-danger">
                                                        <i class="bi bi-exclamation-triangle"></i> ${result.error}
                                                    </div>`
                                                }
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>

                            <div class="mt-3">
                                <h6>Performance Summary</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-success">${comparison.results.filter(r => r.success).length}</div>
                                            <small class="text-muted">Successful</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-danger">${comparison.results.filter(r => !r.success).length}</div>
                                            <small class="text-muted">Failed</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-info">${Math.round(comparison.results.filter(r => r.success).reduce((sum, r) => sum + r.responseTime, 0) / comparison.results.filter(r => r.success).length) || 0}ms</div>
                                            <small class="text-muted">Avg Response</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h4 text-warning">${Math.min(...comparison.results.filter(r => r.success).map(r => r.responseTime)) || 0}ms</div>
                                            <small class="text-muted">Fastest</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="mergeModelResults('${comparisonId}')">
                                <i class="bi bi-layers"></i> Merge Best Results
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('aiComparisonModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('aiComparisonModal'));
        modal.show();
    }

    getComparison(comparisonId) {
        return this.comparisonResults.get(comparisonId);
    }
}

// Analytics Dashboard System
class AnalyticsDashboard {
    constructor() {
        this.usageStats = this.loadUsageStats();
    }

    loadUsageStats() {
        const stored = localStorage.getItem('aiUsageStats');
        return stored ? JSON.parse(stored) : {
            totalRequests: 0,
            requestsByModel: {},
            requestsByTask: {},
            averageResponseTimes: {},
            dailyUsage: {},
            costEstimation: 0
        };
    }

    saveUsageStats() {
        localStorage.setItem('aiUsageStats', JSON.stringify(this.usageStats));
    }

    recordUsage(modelId, taskType, responseTime, fromCache = false) {
        const today = new Date().toISOString().split('T')[0];

        if (!fromCache) {
            this.usageStats.totalRequests++;

            // Track by model
            if (!this.usageStats.requestsByModel[modelId]) {
                this.usageStats.requestsByModel[modelId] = 0;
            }
            this.usageStats.requestsByModel[modelId]++;

            // Track by task type
            if (!this.usageStats.requestsByTask[taskType]) {
                this.usageStats.requestsByTask[taskType] = 0;
            }
            this.usageStats.requestsByTask[taskType]++;

            // Track response times
            if (!this.usageStats.averageResponseTimes[modelId]) {
                this.usageStats.averageResponseTimes[modelId] = [];
            }
            this.usageStats.averageResponseTimes[modelId].push(responseTime);

            // Track daily usage
            if (!this.usageStats.dailyUsage[today]) {
                this.usageStats.dailyUsage[today] = 0;
            }
            this.usageStats.dailyUsage[today]++;

            // Estimate cost (rough estimation)
            this.usageStats.costEstimation += this.estimateRequestCost(modelId);
        }

        this.saveUsageStats();
    }

    estimateRequestCost(modelId) {
        // Rough cost estimation per request (in cents)
        const costMap = {
            'deepseek-r1': 0.5,
            'gemma-3b': 0.1,
            'deepseek-v2': 0.3,
            'qwen3-14b': 0.4
        };
        return costMap[modelId] || 0.2;
    }

    generateDashboard() {
        const modalHtml = `
            <div class="modal fade" id="analyticsDashboard" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-dark text-white">
                            <h5 class="modal-title">
                                <i class="bi bi-graph-up"></i> AI Analytics Dashboard
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${this.generateOverviewCards()}
                            ${this.generateUsageCharts()}
                            ${this.generateRecommendations()}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-danger" onclick="analyticsManager.clearStats()">
                                <i class="bi bi-trash"></i> Clear Stats
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('analyticsDashboard');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('analyticsDashboard'));
        modal.show();
    }

    generateOverviewCards() {
        const totalRequests = this.usageStats.totalRequests;
        const avgCost = this.usageStats.costEstimation;
        const mostUsedModel = Object.entries(this.usageStats.requestsByModel)
            .sort(([,a], [,b]) => b - a)[0]?.[0] || 'None';
        const mostUsedTask = Object.entries(this.usageStats.requestsByTask)
            .sort(([,a], [,b]) => b - a)[0]?.[0] || 'None';

        return `
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3>${totalRequests}</h3>
                            <p class="mb-0">Total Requests</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3>$${avgCost.toFixed(2)}</h3>
                            <p class="mb-0">Estimated Cost</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3>${multiAPIManager.apiConfigs[mostUsedModel]?.name.split(' ')[0] || 'N/A'}</h3>
                            <p class="mb-0">Most Used Model</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body text-center">
                            <h3>${mostUsedTask}</h3>
                            <p class="mb-0">Most Used Task</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateUsageCharts() {
        return `
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Requests by Model</h6>
                        </div>
                        <div class="card-body">
                            ${this.generateModelUsageChart()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Requests by Task Type</h6>
                        </div>
                        <div class="card-body">
                            ${this.generateTaskUsageChart()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateModelUsageChart() {
        const modelData = Object.entries(this.usageStats.requestsByModel);
        const total = modelData.reduce((sum, [, count]) => sum + count, 0);

        if (total === 0) {
            return '<p class="text-muted text-center">No usage data available</p>';
        }

        return modelData.map(([modelId, count]) => {
            const percentage = (count / total * 100).toFixed(1);
            const modelName = multiAPIManager.apiConfigs[modelId]?.name || modelId;

            return `
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>${modelName}</span>
                        <span>${count} (${percentage}%)</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    generateTaskUsageChart() {
        const taskData = Object.entries(this.usageStats.requestsByTask);
        const total = taskData.reduce((sum, [, count]) => sum + count, 0);

        if (total === 0) {
            return '<p class="text-muted text-center">No usage data available</p>';
        }

        return taskData.map(([taskType, count]) => {
            const percentage = (count / total * 100).toFixed(1);

            return `
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>${taskType}</span>
                        <span>${count} (${percentage}%)</span>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-info" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    generateRecommendations() {
        const recommendations = [];

        // Analyze usage patterns and generate recommendations
        const modelUsage = Object.entries(this.usageStats.requestsByModel);
        const mostUsed = modelUsage.sort(([,a], [,b]) => b - a)[0];
        const leastUsed = modelUsage.sort(([,a], [,b]) => a - b)[0];

        if (mostUsed && leastUsed && mostUsed[1] > leastUsed[1] * 3) {
            recommendations.push({
                type: 'balance',
                title: 'Consider Load Balancing',
                message: `${multiAPIManager.apiConfigs[mostUsed[0]]?.name} is heavily used. Consider distributing load to other models.`
            });
        }

        if (this.usageStats.totalRequests > 100) {
            recommendations.push({
                type: 'cache',
                title: 'Cache Optimization',
                message: 'With high usage, consider increasing cache timeout for better performance.'
            });
        }

        if (this.usageStats.costEstimation > 5) {
            recommendations.push({
                type: 'cost',
                title: 'Cost Optimization',
                message: 'Consider using faster, cheaper models for simple tasks to reduce costs.'
            });
        }

        if (recommendations.length === 0) {
            recommendations.push({
                type: 'info',
                title: 'Optimal Usage',
                message: 'Your AI usage patterns look optimal. Keep up the good work!'
            });
        }

        return `
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Recommendations</h6>
                </div>
                <div class="card-body">
                    ${recommendations.map(rec => `
                        <div class="alert alert-${rec.type === 'info' ? 'success' : 'warning'} alert-sm">
                            <strong>${rec.title}:</strong> ${rec.message}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    clearStats() {
        this.usageStats = {
            totalRequests: 0,
            requestsByModel: {},
            requestsByTask: {},
            averageResponseTimes: {},
            dailyUsage: {},
            costEstimation: 0
        };
        this.saveUsageStats();
        showToast('Analytics data cleared', 'success');

        // Close and reopen dashboard to refresh
        const modal = bootstrap.Modal.getInstance(document.getElementById('analyticsDashboard'));
        if (modal) {
            modal.hide();
            setTimeout(() => this.generateDashboard(), 300);
        }
    }
}

// Initialize enhanced features
const modelComparison = new AIModelComparison();
const analyticsManager = new AnalyticsDashboard();

// ===== ENHANCED FEATURES FUNCTIONS =====

// Show model comparison for current bug title
async function showModelComparison() {
    const title = document.getElementById('title').value.trim();

    if (!title) {
        showToast('Please enter a bug title first to compare AI models', 'warning');
        return;
    }

    const prompt = `Analyze this bug report and provide insights: ${title}`;

    try {
        showToast('Comparing AI models... This may take a moment', 'info');

        const comparison = await modelComparison.compareModels(prompt, 'bug-analysis');
        modelComparison.displayComparison(comparison.comparisonId);

        showToast(`Comparison complete! ${comparison.results.filter(r => r.success).length} models responded successfully`, 'success');
    } catch (error) {
        console.error('Model comparison failed:', error);
        showToast('Model comparison failed. Please try again.', 'error');
    }
}

// Select a specific model result from comparison
function selectModelResult(modelId, comparisonId) {
    const comparison = modelComparison.getComparison(comparisonId);
    if (!comparison) return;

    const selectedResult = comparison.results.find(r => r.modelId === modelId);
    if (!selectedResult || !selectedResult.success) return;

    // Apply the selected result to the form
    const descriptionField = document.getElementById('description');
    if (descriptionField && !descriptionField.value.trim()) {
        descriptionField.value = selectedResult.result.substring(0, 1000); // Limit length
    }

    // Close the comparison modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('aiComparisonModal'));
    if (modal) {
        modal.hide();
    }

    showToast(`Applied result from ${selectedResult.modelName}`, 'success');
}

// Merge results from multiple models
function mergeModelResults(comparisonId) {
    const comparison = modelComparison.getComparison(comparisonId);
    if (!comparison) return;

    const successfulResults = comparison.results.filter(r => r.success);
    if (successfulResults.length === 0) return;

    // Create a merged analysis
    const mergedContent = `
**Multi-AI Analysis Summary**

${successfulResults.map((result, index) => `
**${result.modelName} Analysis (${result.responseTime}ms):**
${result.result.substring(0, 300)}${result.result.length > 300 ? '...' : ''}
`).join('\n')}

**Consensus Insights:**
- Multiple AI models analyzed this bug report
- Response times ranged from ${Math.min(...successfulResults.map(r => r.responseTime))}ms to ${Math.max(...successfulResults.map(r => r.responseTime))}ms
- ${successfulResults.length} out of ${comparison.results.length} models provided successful analysis
`;

    // Apply merged content to description
    const descriptionField = document.getElementById('description');
    if (descriptionField) {
        descriptionField.value = mergedContent;
    }

    // Close the comparison modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('aiComparisonModal'));
    if (modal) {
        modal.hide();
    }

    showToast(`Merged insights from ${successfulResults.length} AI models`, 'success');
}

// Enhanced API request tracking for analytics
function trackAPIUsage(apiResponse, taskType) {
    if (apiResponse && apiResponse.apiUsed) {
        analyticsManager.recordUsage(
            apiResponse.apiUsed,
            taskType,
            apiResponse.responseTime,
            apiResponse.fromCache
        );
    }
}

// Initialize settings related elements - enhanced for multi-API support
function initializeSettings() {
    // Set default values in localStorage if not already set
    if (!localStorage.getItem('apiKey')) {
        localStorage.setItem('apiKey', 'AIzaSyBSBV--lZOn2UnHlGdzT-nOlcX9d73p9eY');
    }



    // Initialize multi-API settings
    if (!localStorage.getItem('useMultiAPI')) {
        localStorage.setItem('useMultiAPI', 'true');
    }

    // Initialize advanced API settings
    if (!localStorage.getItem('enableTaskOptimization')) {
        localStorage.setItem('enableTaskOptimization', 'true');
    }
    if (!localStorage.getItem('enableLoadBalancing')) {
        localStorage.setItem('enableLoadBalancing', 'true');
    }
    if (!localStorage.getItem('fallbackStrategy')) {
        localStorage.setItem('fallbackStrategy', 'intelligent');
    }

    // Initialize settings UI event listeners
    initializeSettingsUI();
}

// Initialize settings UI event listeners
function initializeSettingsUI() {
    // Multi-API toggle
    const useMultiAPIToggle = document.getElementById('useMultiAPI');
    if (useMultiAPIToggle) {
        useMultiAPIToggle.checked = localStorage.getItem('useMultiAPI') === 'true';
        useMultiAPIToggle.addEventListener('change', function() {
            localStorage.setItem('useMultiAPI', this.checked);
            updateAPIStatusDisplay();
            showToast(this.checked ? 'Multi-AI system enabled' : 'Multi-AI system disabled', 'info');
        });
    }

    // Task optimization toggle
    const taskOptimizationToggle = document.getElementById('enableTaskOptimization');
    if (taskOptimizationToggle) {
        taskOptimizationToggle.checked = localStorage.getItem('enableTaskOptimization') === 'true';
        taskOptimizationToggle.addEventListener('change', function() {
            localStorage.setItem('enableTaskOptimization', this.checked);
        });
    }

    // Load balancing toggle
    const loadBalancingToggle = document.getElementById('enableLoadBalancing');
    if (loadBalancingToggle) {
        loadBalancingToggle.checked = localStorage.getItem('enableLoadBalancing') === 'true';
        loadBalancingToggle.addEventListener('change', function() {
            localStorage.setItem('enableLoadBalancing', this.checked);
        });
    }

    // Fallback strategy selector
    const fallbackStrategySelect = document.getElementById('fallbackStrategy');
    if (fallbackStrategySelect) {
        fallbackStrategySelect.value = localStorage.getItem('fallbackStrategy') || 'intelligent';
        fallbackStrategySelect.addEventListener('change', function() {
            localStorage.setItem('fallbackStrategy', this.value);
        });
    }

    // Refresh API status button
    const refreshAPIStatusBtn = document.getElementById('refreshAPIStatus');
    if (refreshAPIStatusBtn) {
        refreshAPIStatusBtn.addEventListener('click', function() {
            updateAPIStatusDisplay();
            showToast('API status refreshed', 'success');
        });
    }

    // Initial API status display
    updateAPIStatusDisplay();
}

// Update API status display in settings modal
function updateAPIStatusDisplay() {
    const apiHealthStatus = document.getElementById('apiHealthStatus');
    if (!apiHealthStatus) return;

    const healthData = multiAPIManager.getAPIHealth();

    apiHealthStatus.innerHTML = '';

    healthData.forEach(api => {
        const statusColor = api.isActive ?
            (api.successRate >= 80 ? 'success' : api.successRate >= 60 ? 'warning' : 'danger') :
            'secondary';

        const statusIcon = api.isActive ?
            (api.successRate >= 80 ? 'check-circle' : api.successRate >= 60 ? 'exclamation-triangle' : 'x-circle') :
            'pause-circle';

        const apiCard = document.createElement('div');
        apiCard.className = 'col-md-6 mb-2';
        apiCard.innerHTML = `
            <div class="card border-${statusColor}">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title mb-1 small">${api.name}</h6>
                            <div class="small text-muted">
                                <i class="bi bi-${statusIcon} text-${statusColor}"></i>
                                ${api.successRate}% success
                            </div>
                        </div>
                        <div class="text-end">
                            <div class="badge bg-${statusColor}">${api.isActive ? 'Active' : 'Inactive'}</div>
                            <div class="small text-muted">${api.avgResponseTime}ms avg</div>
                        </div>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar bg-${statusColor}" style="width: ${api.successRate}%"></div>
                    </div>
                </div>
            </div>
        `;
        apiHealthStatus.appendChild(apiCard);
    });

    // Update main status indicator
    multiAPIManager.updateAPIStatus();
}

// DOM Elements
const bugForm = document.getElementById('bugForm');
const bugList = document.getElementById('bugList');
const noBugsMessage = document.getElementById('noBugsMessage');
const exportBtn = document.getElementById('exportBtn');
// Dark mode is always enabled - no toggle needed
const cancelEditBtn = document.getElementById('cancelEdit');
const titleInput = document.getElementById('title');
const descriptionInput = document.getElementById('description');
const severityInput = document.getElementById('severity');
const stepsInput = document.getElementById('stepsToReproduce');
const actualResultInput = document.getElementById('actualResult');
const expectedResultInput = document.getElementById('expectedResult');
const summarizeBenefitsBtn = document.getElementById('summarizeBenefitsBtn');

// Add event listeners for automatic impact analysis update
if (titleInput) {
    titleInput.addEventListener('input', summarizeBug);
}
if (descriptionInput) {
    descriptionInput.addEventListener('input', summarizeBug);
}
if (severityInput) {
    severityInput.addEventListener('change', summarizeBug);
}

// Smart AI Elements
const qualityScoreCard = document.getElementById('qualityScoreCard');
const qualityScore = document.getElementById('qualityScore');
const qualityProgress = document.getElementById('qualityProgress');
const qualityFeedback = document.getElementById('qualityFeedback');
const missingSuggestions = document.getElementById('missingSuggestions');
const titleSuggestions = document.getElementById('titleSuggestions');
const duplicateWarning = document.getElementById('duplicateWarning');
const descriptionSuggestions = document.getElementById('descriptionSuggestions');
const categorySection = document.getElementById('categorySection');
const bugCategory = document.getElementById('bugCategory');
const severitySuggestion = document.getElementById('severitySuggestion');

// Bug Form Submission
bugForm.addEventListener('submit', function(e) {
    e.preventDefault();

    const bugId = document.getElementById('bugId').value;
    const title = titleInput.value;
    let description = descriptionInput.value;
    let severity = severityInput.value;
    let stepsToReproduce = stepsInput.value;
    let actualResult = actualResultInput.value;
    let expectedResult = expectedResultInput.value;

    // If description, severity, steps, actual or expected results are empty, generate them automatically
    if (!description || !severity || !stepsToReproduce || !actualResult || !expectedResult) {
        const generatedDetails = generateBugDetailsFromTitle(title);

        if (!description) description = generatedDetails.description;
        if (!severity) severity = generatedDetails.severity;
        if (!stepsToReproduce) stepsToReproduce = generatedDetails.steps;
        if (!actualResult) actualResult = generatedDetails.actualResult;
        if (!expectedResult) expectedResult = generatedDetails.expectedResult;
    }

    if (bugId) {
        // Update existing bug
        updateBug(bugId, title, description, severity, stepsToReproduce, actualResult, expectedResult);
    } else {
        // Add new bug
        addBug(title, description, severity, stepsToReproduce, actualResult, expectedResult);
    }

    // Reset form
    bugForm.reset();
    document.getElementById('bugId').value = '';
    document.getElementById('submitBug').textContent = 'Submit Bug';
    cancelEditBtn.classList.add('d-none');
});

// Cancel Edit Button
cancelEditBtn.addEventListener('click', function() {
    bugForm.reset();
    document.getElementById('bugId').value = '';
    document.getElementById('submitBug').textContent = 'Submit Bug';
    cancelEditBtn.classList.add('d-none');
});

// Dark mode is always enabled - no toggle needed

// Export Bugs as JSON
exportBtn.addEventListener('click', function() {
    const bugs = getBugsFromStorage();
    if (bugs.length === 0) {
        alert('No bugs to export!');
        return;
    }

    const dataStr = JSON.stringify(bugs, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const exportFileDefaultName = 'bug-report-' + new Date().toISOString().slice(0, 10) + '.json';

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
});

// Add a new bug
function addBug(title, description, severity, stepsToReproduce, actualResult, expectedResult) {
    const bugs = getBugsFromStorage();

    // Calculate quality score
    const qualityScore = calculateBugQualityScore(title, description, severity, stepsToReproduce, actualResult, expectedResult);

    // Detect category
    const detectedCategories = detectBugCategoryFromContent(title + ' ' + description);
    const category = detectedCategories.length > 0 ? detectedCategories[0].category : 'Uncategorized';

    // Get environment info
    const environment = {
        browser: detectBrowser(),
        os: detectOS(),
        screenResolution: `${screen.width}x${screen.height}`,
        userAgent: navigator.userAgent
    };

    const newBug = {
        id: Date.now().toString(),
        title: title,
        description: description,
        severity: severity,
        stepsToReproduce: stepsToReproduce,
        actualResult: actualResult,
        expectedResult: expectedResult,
        dateCreated: new Date().toISOString(),
        bugType: analyzeBugType(title, description),
        category: category,
        qualityScore: qualityScore,
        environment: environment,
        attachments: getUploadedFiles() // Get any uploaded files
    };

    bugs.push(newBug);
    saveBugsToStorage(bugs);

    // Add to UI with animation
    const row = createBugRow(newBug);
    row.classList.add('bug-item-new');
    bugList.prepend(row);

    // Show success message
    showToast('Bug successfully added!', 'success');

    // Hide no bugs message if it was showing
    noBugsMessage.classList.add('d-none');

    // Update analytics
    updateAnalytics();

    // Clear uploaded files
    clearUploadedFiles();

    // Re-initialize copy buttons for the new row
    setTimeout(() => {
        initializeCopyButtons();
    }, 100);
}

// Update an existing bug
function updateBug(id, title, description, severity, stepsToReproduce, actualResult, expectedResult) {
    const bugs = getBugsFromStorage();

    const bugIndex = bugs.findIndex(bug => bug.id === id);
    if (bugIndex !== -1) {
        // No attachment processing - removed as part of title-only focus

        bugs[bugIndex].title = title;
        bugs[bugIndex].description = description;
        bugs[bugIndex].severity = severity;
        bugs[bugIndex].stepsToReproduce = stepsToReproduce;
        bugs[bugIndex].actualResult = actualResult;
        bugs[bugIndex].expectedResult = expectedResult;
        bugs[bugIndex].bugType = analyzeBugType(title, description);

        saveBugsToStorage(bugs);
        loadBugs(); // Refresh the list

        // Show success message
        showToast('Bug successfully updated!', 'info');

        // Clear attachment preview
        const attachmentPreview = document.getElementById('attachmentPreview');
        if (attachmentPreview) {
            attachmentPreview.innerHTML = '';
        }
    }
}

// Filter bugs based on criteria
function filterBugs(criteria = {}) {
    const bugs = getBugsFromStorage();

    return bugs.filter(bug => {
        // Check each criteria
        if (criteria.status && bug.status !== criteria.status) return false;
        if (criteria.severity && bug.severity !== criteria.severity) return false;
        if (criteria.category && bug.category !== criteria.category) return false;
        if (criteria.priority && bug.priority !== criteria.priority) return false;
        if (criteria.assignedTo && bug.assignedTo !== criteria.assignedTo) return false;

        // Search in title and description
        if (criteria.searchTerm) {
            const searchTerm = criteria.searchTerm.toLowerCase();
            const inTitle = bug.title.toLowerCase().includes(searchTerm);
            const inDescription = bug.description.toLowerCase().includes(searchTerm);
            if (!inTitle && !inDescription) return false;
        }

        return true;
    });
}

// Delete a bug
function deleteBug(id) {
    if (!confirm('Are you sure you want to delete this bug?')) {
        return;
    }

    const row = document.querySelector(`tr[data-id="${id}"]`);
    if (row) {
        // Add delete animation
        row.classList.add('bug-item-delete');

        // Remove from storage and UI after animation completes
        const bugs = getBugsFromStorage().filter(bug => bug.id !== id);
        saveBugsToStorage(bugs);
        updateAnalytics(); // Ensure summary statistics are updated

        // Remove from UI after animation completes
        setTimeout(() => {
            row.remove();

            // Show no bugs message if no bugs left
            if (bugs.length === 0) {
                noBugsMessage.classList.remove('d-none');
            }

            // Show success message
            showToast('Bug successfully deleted!', 'danger');
        }, 500); // Match animation duration
    }
}

// Edit a bug (load into form)
function editBug(id) {
    const bugs = getBugsFromStorage();
    const bug = bugs.find(bug => bug.id === id);

    if (bug) {
        document.getElementById('bugId').value = bug.id;
        document.getElementById('title').value = bug.title;
        document.getElementById('description').value = bug.description;
        document.getElementById('severity').value = bug.severity;
        document.getElementById('stepsToReproduce').value = bug.stepsToReproduce || '';
        document.getElementById('actualResult').value = bug.actualResult || '';
        document.getElementById('expectedResult').value = bug.expectedResult || '';

        document.getElementById('submitBug').textContent = 'Update Bug';
        cancelEditBtn.classList.remove('d-none');

        // Scroll to form
        bugForm.scrollIntoView({ behavior: 'smooth' });
    }
}

// Print a single bug report
function printBug(id) {
    const bugs = getBugsFromStorage();
    const bug = bugs.find(bug => bug.id === id);

    if (!bug) {
        showToast('Bug not found!', 'error');
        return;
    }

    const dateCreated = new Date(bug.dateCreated).toLocaleDateString();

    // Create a printable HTML content for the single bug
    const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Bug Report - ${bug.title}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 10px;
                    margin-bottom: 20px;
                }
                .bug-title {
                    color: #007bff;
                    margin-bottom: 10px;
                    font-size: 24px;
                }
                .severity-critical { color: #dc3545; font-weight: bold; }
                .severity-high { color: #fd7e14; font-weight: bold; }
                .severity-medium { color: #ffc107; font-weight: bold; }
                .severity-low { color: #28a745; font-weight: bold; }
                .section {
                    margin-bottom: 20px;
                    padding: 15px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .section-title {
                    font-weight: bold;
                    color: #495057;
                    margin-bottom: 8px;
                    font-size: 16px;
                }
                .meta-info {
                    background: #f8f9fa;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }
                .footer {
                    margin-top: 30px;
                    padding-top: 15px;
                    border-top: 1px solid #ddd;
                    font-size: 12px;
                    color: #6c757d;
                    text-align: center;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1 class="bug-title">${bug.title}</h1>
                <div class="meta-info">
                    <strong>Bug ID:</strong> ${bug.id} |
                    <strong>Severity:</strong> <span class="severity-${bug.severity.toLowerCase()}">${bug.severity}</span> |
                    <strong>Category:</strong> ${bug.category || 'Uncategorized'} |
                    <strong>Quality Score:</strong> ${bug.qualityScore || 0}% |
                    <strong>Date Created:</strong> ${dateCreated}
                </div>
            </div>

            <div class="section">
                <div class="section-title">Description</div>
                <div>${bug.description || 'No description provided'}</div>
            </div>

            ${bug.stepsToReproduce ? `
            <div class="section">
                <div class="section-title">Steps to Reproduce</div>
                <div style="white-space: pre-wrap;">${bug.stepsToReproduce}</div>
            </div>
            ` : ''}

            ${bug.actualResult ? `
            <div class="section">
                <div class="section-title">Actual Result</div>
                <div>${bug.actualResult}</div>
            </div>
            ` : ''}

            ${bug.expectedResult ? `
            <div class="section">
                <div class="section-title">Expected Result</div>
                <div>${bug.expectedResult}</div>
            </div>
            ` : ''}

            ${bug.environment ? `
            <div class="section">
                <div class="section-title">Environment Information</div>
                <div>
                    <strong>Browser:</strong> ${bug.environment.browser || 'N/A'}<br>
                    <strong>Operating System:</strong> ${bug.environment.os || 'N/A'}<br>
                    <strong>Screen Resolution:</strong> ${bug.environment.screenResolution || 'N/A'}
                </div>
            </div>
            ` : ''}

            <div class="footer">
                <p>Generated by Bug Reporter App on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                <p>Made with ❤️ by Ahmed Gamal</p>
            </div>
        </body>
        </html>
    `;

    // Open in new window for printing
    const newWindow = window.open('', '_blank');
    if (newWindow) {
        try {
            // Use modern approach with innerHTML
            newWindow.document.documentElement.innerHTML = htmlContent;
            newWindow.document.close();

            // Auto-print after a short delay to ensure content is loaded
            setTimeout(() => {
                newWindow.print();
            }, 500);

        } catch (error) {
            console.warn('Modern approach failed, using fallback:', error);
            // Fallback to document.write with error handling
            try {
                newWindow.document.write(htmlContent);
                newWindow.document.close();
                setTimeout(() => {
                    newWindow.print();
                }, 500);
            } catch (fallbackError) {
                console.error('Print generation failed:', fallbackError);
                showToast('Failed to print bug report. Please try again.', 'error');
                newWindow.close();
                return;
            }
        }
    } else {
        showToast('Failed to open print window. Please check popup blocker.', 'error');
        return;
    }

    showToast('Print window opened!', 'success');
}

// ===== ADVANCED TEST CASE GENERATION SYSTEM =====

// Generate comprehensive test cases based on bug title and description
async function generateAdvancedTestCases() {
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();

    if (!title) {
        showToast('Please enter a bug title first!', 'warning');
        titleInput.focus();
        return;
    }

    // Show loading state
    const aiEnhanceBtn = document.getElementById('aiEnhanceBtn');
    const originalContent = aiEnhanceBtn.innerHTML;
    aiEnhanceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
    aiEnhanceBtn.disabled = true;

    try {
        // Generate comprehensive test cases using advanced algorithm (now async)
        const testCases = await generateComprehensiveTestCases(title, description);

        // Display test cases in a modal
        displayTestCasesModal(testCases);

        showToast('Advanced test cases generated successfully!', 'success');
    } catch (error) {
        console.error('Error generating test cases:', error);
        showToast('Failed to generate test cases. Please try again.', 'error');
    } finally {
        // Reset button
        aiEnhanceBtn.innerHTML = originalContent;
        aiEnhanceBtn.disabled = false;
    }
}

// Advanced semantic analysis and context-aware test case generation with Multi-API backend
async function generateComprehensiveTestCases(title, description) {
    // Check if multi-API is enabled
    const useMultiAPI = localStorage.getItem('useMultiAPI') === 'true';

    if (useMultiAPI) {
        try {
            console.log('🚀 Using Multi-API system for test case generation');

            // Create comprehensive prompt for test case generation
            const prompt = `
Analyze this bug report and generate 8-12 comprehensive test cases:

**Bug Title:** ${title}
**Description:** ${description || 'No description provided'}

Generate test cases in the following JSON format:
{
    "test_cases": [
        {
            "id": "TC_001",
            "title": "Test case title",
            "description": "Detailed description",
            "type": "Reproduction|Validation|Edge Case|Regression|Performance|Security|Accessibility|Integration",
            "priority": "High|Medium|Low",
            "steps": ["Step 1", "Step 2", "Step 3"],
            "expectedResult": "Expected outcome",
            "preconditions": ["Precondition 1"],
            "testData": {"key": "value"},
            "automationHints": ["Hint 1"]
        }
    ],
    "quality_score": 0.87,
    "metadata": {
        "total_test_cases": 10,
        "coverage_areas": ["UI", "Functionality", "Performance"],
        "complexity_score": 0.75
    }
}

Focus on:
1. Reproduction test cases to verify the bug
2. Validation test cases for fix verification
3. Edge cases and boundary conditions
4. Regression tests for related functionality
5. Performance impact tests
6. Security considerations if applicable
7. Accessibility compliance
8. Integration with other components

Make test cases specific to the reported issue, not generic templates.
`;

            const apiResponse = await multiAPIManager.makeAPIRequest(prompt, 'test-generation');

            // Parse the AI response
            let aiTestCases;
            try {
                // Try to extract JSON from the response
                const jsonMatch = apiResponse.result.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    aiTestCases = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('No JSON found in response');
                }
            } catch (parseError) {
                console.warn('Failed to parse AI response as JSON, using fallback parsing');
                aiTestCases = parseAIResponseToTestCases(apiResponse.result, title, description);
            }

            if (aiTestCases && aiTestCases.test_cases && aiTestCases.test_cases.length > 0) {
                console.log(`✅ Generated ${aiTestCases.test_cases.length} test cases using ${multiAPIManager.apiConfigs[apiResponse.apiUsed].name}`);

                // Track usage for analytics
                trackAPIUsage(apiResponse, 'test-generation');

                // Add metadata about the AI generation
                aiTestCases.metadata = {
                    ...aiTestCases.metadata,
                    title: title,
                    description: description,
                    generatedAt: new Date().toISOString(),
                    aiGenerated: true,
                    apiUsed: apiResponse.apiUsed,
                    responseTime: apiResponse.responseTime,
                    multiAPIMode: true
                };

                return convertMultiAPITestCasesToFormat(aiTestCases);
            }
        } catch (error) {
            console.warn('Multi-API test generation failed, falling back to local AI:', error);
        }
    }

    // Try AI backend first (if available)
    if (window.AIBugIntelligence && window.AIBugIntelligence.isConnected) {
        try {
            const aiTestCases = await window.AIBugIntelligence.generateTestCases(title, description, {
                mode: 'intelligent'
            });

            if (aiTestCases && !aiTestCases.fallback) {
                return convertAITestCasesToFormat(aiTestCases);
            }
        } catch (error) {
            console.warn('AI backend not available, using intelligent fallback:', error);
        }
    }

    // Use production AI system (Python backend with JavaScript fallback)
    try {
        const aiResult = await productionAI.generateTestCases(title, description);
        return convertHybridAIToFormat(aiResult);
    } catch (error) {
        console.warn('Production AI failed, using basic fallback:', error);
    }

    // Fallback to original semantic analysis
    const testCases = {
        metadata: {
            title: title,
            description: description,
            generatedAt: new Date().toISOString(),
            totalTestCases: 0
        },
        categories: {}
    };

    // Perform deep semantic analysis of the bug report
    const semanticContext = performSemanticAnalysis(title, description);

    // Generate highly specific test cases based on semantic understanding
    testCases.categories.reproduction = generateReproductionTestCases(semanticContext);
    testCases.categories.validation = generateValidationTestCases(semanticContext);
    testCases.categories.edgeCases = generateSpecificEdgeCases(semanticContext);
    testCases.categories.relatedScenarios = generateRelatedScenarios(semanticContext);
    testCases.categories.regression = generateTargetedRegressionTests(semanticContext);

    // Calculate total test cases
    testCases.metadata.totalTestCases = Object.values(testCases.categories)
        .reduce((total, category) => total + category.tests.length, 0);

    return testCases;
}

// Parse AI response to test cases when JSON parsing fails
function parseAIResponseToTestCases(response, title, description) {
    const testCases = [];
    let testCaseCounter = 1;

    // Split response into sections and extract test cases
    const lines = response.split('\n');
    let currentTestCase = null;
    let currentSection = '';

    for (const line of lines) {
        const trimmedLine = line.trim();

        if (trimmedLine.toLowerCase().includes('test case') ||
            trimmedLine.toLowerCase().includes('tc_') ||
            trimmedLine.match(/^\d+\./)) {

            // Save previous test case
            if (currentTestCase) {
                testCases.push(currentTestCase);
            }

            // Start new test case
            currentTestCase = {
                id: `TC_${String(testCaseCounter).padStart(3, '0')}`,
                title: trimmedLine.replace(/^\d+\.?\s*/, '').replace(/test case:?\s*/i, ''),
                description: '',
                type: 'Functional',
                priority: 'Medium',
                steps: [],
                expectedResult: '',
                preconditions: [],
                testData: {},
                automationHints: []
            };
            testCaseCounter++;
        } else if (currentTestCase) {
            if (trimmedLine.toLowerCase().includes('steps:') ||
                trimmedLine.toLowerCase().includes('procedure:')) {
                currentSection = 'steps';
            } else if (trimmedLine.toLowerCase().includes('expected:') ||
                       trimmedLine.toLowerCase().includes('result:')) {
                currentSection = 'expected';
            } else if (trimmedLine.toLowerCase().includes('description:')) {
                currentSection = 'description';
            } else if (trimmedLine && currentSection === 'steps') {
                currentTestCase.steps.push(trimmedLine.replace(/^[-*]\s*/, ''));
            } else if (trimmedLine && currentSection === 'expected') {
                currentTestCase.expectedResult = trimmedLine;
            } else if (trimmedLine && currentSection === 'description') {
                currentTestCase.description = trimmedLine;
            }
        }
    }

    // Save last test case
    if (currentTestCase) {
        testCases.push(currentTestCase);
    }

    // If no test cases were parsed, create basic ones
    if (testCases.length === 0) {
        testCases.push(
            {
                id: 'TC_001',
                title: `Reproduce: ${title}`,
                description: 'Verify the reported bug can be reproduced',
                type: 'Reproduction',
                priority: 'High',
                steps: ['Navigate to the application', 'Perform the action that triggers the issue', 'Observe the behavior'],
                expectedResult: 'Issue should be reproducible as described',
                preconditions: [],
                testData: {},
                automationHints: []
            },
            {
                id: 'TC_002',
                title: `Validate fix for: ${title}`,
                description: 'Verify the fix resolves the issue',
                type: 'Validation',
                priority: 'High',
                steps: ['Apply the fix', 'Perform the previously failing action', 'Verify expected behavior'],
                expectedResult: 'Issue should be resolved',
                preconditions: [],
                testData: {},
                automationHints: []
            }
        );
    }

    return {
        test_cases: testCases,
        quality_score: 0.85,
        metadata: {
            total_test_cases: testCases.length,
            coverage_areas: ['Functionality', 'Validation'],
            complexity_score: 0.7,
            parsed_from_text: true
        }
    };
}

// Convert Multi-API test cases to our format
function convertMultiAPITestCasesToFormat(aiTestCases) {
    const categories = {};

    // Group test cases by type
    aiTestCases.test_cases.forEach(testCase => {
        const categoryKey = testCase.type.toLowerCase().replace(/\s+/g, '');

        if (!categories[categoryKey]) {
            categories[categoryKey] = {
                category: testCase.type,
                description: `AI-generated ${testCase.type} test cases (Multi-API Enhanced)`,
                tests: [],
                count: 0
            };
        }

        categories[categoryKey].tests.push({
            id: testCase.id,
            title: testCase.title,
            description: testCase.description,
            steps: Array.isArray(testCase.steps) ? testCase.steps : [testCase.steps],
            expectedResult: testCase.expectedResult || testCase.expected_result,
            priority: testCase.priority,
            type: testCase.type,
            preconditions: testCase.preconditions || [],
            testData: testCase.testData || {},
            automationHints: testCase.automationHints || []
        });
        categories[categoryKey].count++;
    });

    return {
        metadata: {
            ...aiTestCases.metadata,
            aiGenerated: true,
            multiAPIEnhanced: true
        },
        categories: categories
    };
}




// Convert AI test cases to our format
function convertAITestCasesToFormat(aiTestCases) {
    const categories = {};

    // Group test cases by type
    aiTestCases.test_cases.forEach(testCase => {
        const categoryKey = testCase.type.toLowerCase().replace(/\s+/g, '');

        if (!categories[categoryKey]) {
            categories[categoryKey] = {
                category: testCase.type,
                description: `AI-generated ${testCase.type} test cases`,
                tests: [],
                count: 0
            };
        }

        categories[categoryKey].tests.push({
            id: testCase.id,
            title: testCase.title,
            description: testCase.description,
            steps: testCase.steps,
            expectedResult: testCase.expectedResult || testCase.expected_result,
            priority: testCase.priority,
            type: testCase.type
        });
        categories[categoryKey].count++;
    });

    return {
        metadata: {
            title: aiTestCases.metadata?.title || '',
            description: aiTestCases.metadata?.description || '',
            generatedAt: new Date().toISOString(),
            totalTestCases: aiTestCases.test_cases.length,
            aiGenerated: true,
            qualityScore: aiTestCases.quality_score
        },
        categories: categories
    };
}

// Convert offline AI results to our format
function convertOfflineAIToFormat(aiResult) {
    return convertAITestCasesToFormat(aiResult);
}

// Convert hybrid AI results to our format
function convertHybridAIToFormat(aiResult) {
    if (aiResult.enhanced_mode) {
        // Python backend format - already structured
        const categories = {};

        // Group test cases by type
        aiResult.test_cases.forEach(testCase => {
            const categoryKey = testCase.type.toLowerCase().replace(/\s+/g, '');

            if (!categories[categoryKey]) {
                categories[categoryKey] = {
                    category: testCase.type,
                    description: `AI-generated ${testCase.type} test cases (Enhanced Mode)`,
                    tests: [],
                    count: 0
                };
            }

            categories[categoryKey].tests.push({
                id: testCase.id,
                title: testCase.title,
                description: testCase.description,
                steps: testCase.steps,
                expectedResult: testCase.expected_result,
                priority: testCase.priority,
                type: testCase.type,
                preconditions: testCase.preconditions || [],
                testData: testCase.test_data || {},
                automationHints: testCase.automation_hints || []
            });
            categories[categoryKey].count++;
        });

        return {
            metadata: {
                title: aiResult.metadata?.title || '',
                description: aiResult.metadata?.description || '',
                generatedAt: new Date().toISOString(),
                totalTestCases: aiResult.test_cases.length,
                aiGenerated: true,
                enhancedMode: true,
                aiBackend: 'python',
                qualityScore: aiResult.quality_score,
                complexityScore: aiResult.metadata?.complexity_score,
                coverageAreas: aiResult.metadata?.coverage_areas
            },
            categories: categories
        };
    } else {
        // JavaScript fallback format
        return convertAITestCasesToFormat(aiResult);
    }
}

// GitHub Pages AI System - JavaScript Only
class GitHubPagesAISystem {
    constructor() {
        this.initializeGitHubPagesMode();
    }

    initializeGitHubPagesMode() {
        console.log('🚀 GitHub Pages AI System initialized');
        console.log('📍 Mode: Static JavaScript AI');
        this.updateUIForGitHubPages();
    }

    updateUIForGitHubPages() {
        const aiButton = document.getElementById('aiEnhanceBtn');
        if (aiButton) {
            aiButton.innerHTML = '<i class="bi bi-robot"></i> Smart Analysis';
            aiButton.title = 'Get intelligent AI-powered bug analysis and recommendations';
        }

        // Add GitHub Pages mode indicator
        this.addModeIndicator();
    }

    addModeIndicator() {
        // Remove existing indicator
        const existingIndicator = document.getElementById('aiModeIndicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Create user-friendly indicator
        const indicator = document.createElement('div');
        indicator.id = 'aiModeIndicator';
        indicator.className = `alert alert-info alert-sm mt-2`;
        indicator.innerHTML = `
            <i class="bi bi-lightbulb"></i>
            <strong>AI Assistant Ready:</strong> Get intelligent analysis and smart recommendations
        `;

        // Insert after AI button
        const aiButton = document.getElementById('aiEnhanceBtn');
        if (aiButton) {
            aiButton.parentNode.insertBefore(indicator, aiButton.nextSibling);
        }
    }

    async analyzeBug(title, description) {
        return await this.analyzeBugWithJavaScript(title, description);
    }

    async analyzeBugWithJavaScript(title, description) {
        // Use the existing JavaScript AI engine for GitHub Pages
        const fullText = `${title} ${description}`;

        // Perform comprehensive analysis
        const entities = this.extractEntities(fullText);
        const predictedSeverity = this.predictSeverity(fullText);
        const predictedCategory = this.predictCategory(fullText);
        const sentimentScore = this.calculateSentimentScore(fullText);
        const urgencyScore = this.calculateUrgencyScore(fullText, entities);
        const recommendations = this.generateRecommendations(title, description, predictedSeverity, predictedCategory, entities);
        const confidenceScores = this.calculateConfidenceScores(fullText, entities, predictedSeverity, predictedCategory);

        const analysis = {
            entities,
            predicted_severity: predictedSeverity,
            predicted_category: predictedCategory,
            sentiment_score: sentimentScore,
            urgency_score: urgencyScore,
            confidence_scores: confidenceScores,
            recommendations: recommendations,
            suggested_description: description || this.generateSuggestedDescription(title, entities),
            analysis_timestamp: new Date().toISOString(),
            ai_version: "1.0.0-github-pages",
            ai_backend: 'javascript-static'
        };

        return analysis;
    }

    // JavaScript AI methods for GitHub Pages
    extractEntities(text) {
        const textLower = text.toLowerCase();
        const entities = {
            ui_components: [],
            actions: [],
            error_types: [],
            file_types: []
        };

        // UI Components
        const uiPattern = /\b(button|btn|field|input|dropdown|select|menu|modal|dialog|form|table|grid|list|image|icon|link|tab|panel)\b/g;
        const uiMatches = textLower.match(uiPattern) || [];
        entities.ui_components = [...new Set(uiMatches)];

        // Actions
        const actionPattern = /\b(click|tap|press|submit|save|delete|edit|create|update|search|filter|sort|upload|download|login|logout)\b/g;
        const actionMatches = textLower.match(actionPattern) || [];
        entities.actions = [...new Set(actionMatches)];

        // Error types
        const errorPattern = /\b(error|fail|failed|failure|broken|crash|crashed|freeze|frozen|hang|stuck|slow|lag|timeout)\b/g;
        const errorMatches = textLower.match(errorPattern) || [];
        entities.error_types = [...new Set(errorMatches)];

        // File types
        const filePattern = /\b(png|jpg|jpeg|gif|pdf|doc|docx|xls|xlsx|csv|txt|zip|mp4|avi|mp3)\b/g;
        const fileMatches = textLower.match(filePattern) || [];
        entities.file_types = [...new Set(fileMatches)];

        return entities;
    }

    predictSeverity(text) {
        const textLower = text.toLowerCase();
        const severityKeywords = {
            'critical': ['crash', 'hang', 'freeze', 'data loss', 'security', 'critical', 'urgent', 'production down'],
            'high': ['error', 'fail', 'broken', 'not working', 'major', 'important', 'blocking', 'prevents'],
            'medium': ['issue', 'problem', 'bug', 'incorrect', 'wrong', 'unexpected', 'should'],
            'low': ['cosmetic', 'minor', 'suggestion', 'enhancement', 'typo', 'alignment', 'color']
        };

        const scores = {};
        for (const severity of Object.keys(severityKeywords)) {
            scores[severity] = 0;
        }

        for (const [severity, keywords] of Object.entries(severityKeywords)) {
            for (const keyword of keywords) {
                if (textLower.includes(keyword)) {
                    scores[severity] += 1;
                    if (textLower.includes(` ${keyword} `) || textLower.startsWith(keyword) || textLower.endsWith(keyword)) {
                        scores[severity] += 0.5;
                    }
                }
            }
        }

        const maxScore = Math.max(...Object.values(scores));
        if (maxScore === 0) return 'Medium';

        const result = Object.keys(scores).find(key => scores[key] === maxScore) || 'Medium';
        return result.charAt(0).toUpperCase() + result.slice(1);
    }

    predictCategory(text) {
        const textLower = text.toLowerCase();
        const categoryKeywords = {
            'ui': ['button', 'layout', 'display', 'visual', 'interface', 'design', 'css', 'style', 'responsive'],
            'functional': ['feature', 'function', 'work', 'functionality', 'operation', 'process', 'workflow'],
            'performance': ['slow', 'lag', 'freeze', 'timeout', 'loading', 'speed', 'memory', 'delay'],
            'security': ['login', 'password', 'auth', 'permission', 'access', 'security', 'vulnerability'],
            'data': ['database', 'data', 'save', 'load', 'corrupt', 'missing', 'duplicate', 'sync']
        };

        const scores = {};
        for (const category of Object.keys(categoryKeywords)) {
            scores[category] = 0;
        }

        for (const [category, keywords] of Object.entries(categoryKeywords)) {
            for (const keyword of keywords) {
                if (textLower.includes(keyword)) {
                    scores[category] += 1;
                    const wordBoundaryRegex = new RegExp(`\\b${keyword}\\b`, 'i');
                    if (wordBoundaryRegex.test(text)) {
                        scores[category] += 0.5;
                    }
                }
            }
        }

        const maxScore = Math.max(...Object.values(scores));
        if (maxScore === 0) return 'functional';

        const result = Object.keys(scores).find(key => scores[key] === maxScore) || 'functional';
        return result;
    }

    calculateSentimentScore(text) {
        const positiveWords = ['good', 'great', 'excellent', 'perfect', 'works', 'fixed', 'resolved'];
        const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'broken', 'failed', 'wrong'];

        const textLower = text.toLowerCase();
        const positiveCount = positiveWords.filter(word => textLower.includes(word)).length;
        const negativeCount = negativeWords.filter(word => textLower.includes(word)).length;

        if (positiveCount + negativeCount === 0) return 0.5;
        return positiveCount / (positiveCount + negativeCount);
    }

    calculateUrgencyScore(text, entities) {
        let urgencyScore = 0.0;
        const textLower = text.toLowerCase();

        const criticalErrors = ['crash', 'freeze', 'hang', 'fail', 'error'];
        if (entities.error_types.some(error => criticalErrors.includes(error))) {
            urgencyScore += 0.3;
        }

        const impactKeywords = ['production', 'all users', 'critical', 'urgent', 'blocking'];
        if (impactKeywords.some(keyword => textLower.includes(keyword))) {
            urgencyScore += 0.4;
        }

        const frequencyKeywords = ['always', 'repeatedly', 'multiple times'];
        if (frequencyKeywords.some(freq => textLower.includes(freq))) {
            urgencyScore += 0.3;
        }

        return Math.min(urgencyScore, 1.0);
    }

    generateSuggestedDescription(title, entities) {
        const components = entities.ui_components.join(', ') || 'component';
        const actions = entities.actions.join(', ') || 'interaction';
        const errors = entities.error_types.join(', ') || 'issue';

        return `The ${components} experiences ${errors} when users attempt ${actions}. This affects the normal workflow and needs investigation.`;
    }

    generateRecommendations(title, description, severity, category, entities) {
        const recommendations = [];

        recommendations.push({
            type: "priority",
            title: "Priority Recommendation",
            recommendation: `Based on AI analysis, this should be treated as ${severity} priority`,
            confidence: 0.85
        });

        if (category === 'ui') {
            recommendations.push({
                type: "testing",
                title: "UI Testing Suggestion",
                recommendation: "Consider testing across different browsers and screen sizes",
                confidence: 0.80
            });
        } else if (category === 'performance') {
            recommendations.push({
                type: "testing",
                title: "Performance Testing",
                recommendation: "Monitor response times and resource usage during testing",
                confidence: 0.85
            });
        } else if (category === 'security') {
            recommendations.push({
                type: "security",
                title: "Security Review",
                recommendation: "This issue may require security team review before deployment",
                confidence: 0.90
            });
        }

        if (entities.ui_components.length > 0) {
            recommendations.push({
                type: "component",
                title: "Component Testing",
                recommendation: `Focus testing on ${entities.ui_components.join(', ')} components`,
                confidence: 0.75
            });
        }

        if (severity === 'Critical' || severity === 'High') {
            recommendations.push({
                type: "urgency",
                title: "Immediate Action Required",
                recommendation: "This issue should be addressed in the current sprint",
                confidence: 0.90
            });
        }

        return recommendations;
    }

    calculateConfidenceScores(text, entities, severity, category) {
        let severityConfidence = 0.70;
        let categoryConfidence = 0.70;
        let entitiesConfidence = 0.80;

        const wordCount = text.split(' ').length;
        if (wordCount > 10) {
            severityConfidence += 0.10;
            categoryConfidence += 0.10;
        }
        if (wordCount > 20) {
            severityConfidence += 0.05;
            categoryConfidence += 0.05;
        }

        const totalEntities = Object.values(entities).reduce((sum, arr) => sum + arr.length, 0);
        if (totalEntities > 2) {
            entitiesConfidence += 0.10;
            severityConfidence += 0.05;
            categoryConfidence += 0.05;
        }

        return {
            severity: Math.min(severityConfidence, 0.95),
            category: Math.min(categoryConfidence, 0.95),
            entities: Math.min(entitiesConfidence, 0.95)
        };
    }

    async generateTestCases(title, description) {
        return await this.generateTestCasesWithJavaScript(title, description);
    }

    async generateTestCasesWithJavaScript(title, description) {
        const entities = this.extractEntities(`${title} ${description}`);
        const testCases = [];

        // Basic reproduction test
        testCases.push({
            id: "AI_REPRO_001",
            title: `Reproduce: ${title}`,
            description: `AI-generated test to reproduce the reported issue`,
            priority: "High",
            type: "Reproduction",
            steps: [
                "Navigate to the application",
                "Locate the affected component",
                "Perform the action that triggers the issue",
                "Observe and document the behavior"
            ],
            expectedResult: "Issue should be reproducible as described in the bug report"
        });

        // Validation test
        testCases.push({
            id: "AI_VALID_001",
            title: `Validate fix for: ${title}`,
            description: `AI-generated validation test for the fix`,
            priority: "High",
            type: "Validation",
            steps: [
                "Navigate to the application",
                "Perform the previously failing action",
                "Verify the expected behavior occurs",
                "Test related functionality for regressions"
            ],
            expectedResult: "Component should work as expected without the reported issue"
        });

        // Component-specific tests
        entities.ui_components.slice(0, 2).forEach((component, i) => {
            testCases.push({
                id: `AI_COMP_${String(i + 1).padStart(3, '0')}`,
                title: `Test ${component} functionality`,
                description: `AI-generated comprehensive test of ${component} component`,
                priority: "Medium",
                type: "Component",
                steps: [
                    `Navigate to the ${component}`,
                    `Test basic ${component} functionality`,
                    `Test ${component} with various inputs`,
                    `Verify ${component} error handling`
                ],
                expectedResult: `${component.charAt(0).toUpperCase() + component.slice(1)} should work correctly in all scenarios`
            });
        });

        // Add more test types to reach 8-12 tests
        const additionalTests = [
            {
                id: "AI_REGR_001",
                title: "Regression Test",
                description: "Ensure fix doesn't break existing functionality",
                priority: "Medium",
                type: "Regression",
                steps: [
                    "Test all related features",
                    "Verify existing workflows still work",
                    "Check for side effects"
                ],
                expectedResult: "All existing functionality should remain intact"
            },
            {
                id: "AI_EDGE_001",
                title: "Edge Case Test",
                description: "Test boundary conditions and edge cases",
                priority: "Medium",
                type: "Edge Case",
                steps: [
                    "Test with minimum values",
                    "Test with maximum values",
                    "Test with invalid inputs"
                ],
                expectedResult: "System should handle edge cases gracefully"
            },
            {
                id: "AI_PERF_001",
                title: "Performance Test",
                description: "Verify performance is not degraded",
                priority: "Low",
                type: "Performance",
                steps: [
                    "Measure response times",
                    "Monitor resource usage",
                    "Test under load"
                ],
                expectedResult: "Performance should meet requirements"
            },
            {
                id: "AI_SEC_001",
                title: "Security Test",
                description: "Verify security aspects",
                priority: "High",
                type: "Security",
                steps: [
                    "Test authentication",
                    "Verify authorization",
                    "Check for vulnerabilities"
                ],
                expectedResult: "Security measures should be effective"
            },
            {
                id: "AI_ACCESS_001",
                title: "Accessibility Test",
                description: "Verify accessibility compliance",
                priority: "Medium",
                type: "Accessibility",
                steps: [
                    "Test with screen reader",
                    "Verify keyboard navigation",
                    "Check color contrast"
                ],
                expectedResult: "Should meet accessibility standards"
            },
            {
                id: "AI_INTEG_001",
                title: "Integration Test",
                description: "Test integration with other systems",
                priority: "Medium",
                type: "Integration",
                steps: [
                    "Test data flow",
                    "Verify API calls",
                    "Check system interactions"
                ],
                expectedResult: "Integration should work seamlessly"
            }
        ];

        testCases.push(...additionalTests);

        return {
            test_cases: testCases,
            quality_score: 0.85,
            metadata: {
                total_test_cases: testCases.length,
                ai_generated: true,
                github_pages_mode: true
            }
        };
    }

}

// Initialize GitHub Pages AI system
const productionAI = new GitHubPagesAISystem();

// Enhanced bug analysis function using Multi-API System with performance optimizations
async function enhanceBugReport() {
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();

    if (!title) {
        showToast('Please enter a bug title first!', 'warning');
        titleInput.focus();
        return;
    }

    const aiEnhanceBtn = document.getElementById('aiEnhanceBtn');
    const originalContent = aiEnhanceBtn.innerHTML;

    // Check if there's already a request in progress
    if (aiEnhanceBtn.disabled) {
        // Cancel existing request and start new one
        cancellationManager.cancelRequest('aiEnhanceBtn');
        progressTracker.cancelProgress('aiEnhanceBtn');
    }

    aiEnhanceBtn.disabled = true;

    try {
        // Check if multi-API is enabled
        const useMultiAPI = localStorage.getItem('useMultiAPI') === 'true';
        let analysis;

        if (useMultiAPI) {
            console.log('🚀 Using Multi-API system for bug analysis');

            // Create comprehensive prompt for bug analysis
            const prompt = `
Analyze this bug report and provide comprehensive insights:

**Bug Title:** ${title}
**Description:** ${description || 'No description provided'}

Provide analysis in the following JSON format:
{
    "predicted_severity": "Critical|High|Medium|Low",
    "predicted_category": "UI/UX|Performance|Security|Functional|Integration|Mobile",
    "confidence_scores": {
        "severity": 0.85,
        "category": 0.90
    },
    "entities": {
        "ui_components": ["button", "form"],
        "actions": ["click", "submit"],
        "error_types": ["crash", "freeze"],
        "technologies": ["javascript", "react"]
    },
    "sentiment_score": 0.3,
    "urgency_score": 0.7,
    "suggested_description": "Enhanced description if original is missing",
    "recommendations": [
        {
            "type": "priority|testing|security|performance",
            "title": "Recommendation title",
            "recommendation": "Detailed recommendation",
            "confidence": 0.85
        }
    ],
    "quality_assessment": {
        "completeness": 0.8,
        "clarity": 0.9,
        "reproducibility": 0.7
    },
    "related_areas": ["authentication", "user interface"],
    "impact_analysis": {
        "user_impact": "High|Medium|Low",
        "business_impact": "Critical|High|Medium|Low",
        "technical_complexity": "High|Medium|Low"
    }
}

Focus on:
1. Accurate severity prediction based on impact and urgency
2. Precise categorization of the bug type
3. Entity extraction for components, actions, and technologies
4. Quality assessment of the bug report
5. Actionable recommendations for testing and resolution
6. Impact analysis for prioritization
`;

            try {
                const apiResponse = await multiAPIManager.makeAPIRequest(prompt, 'bug-analysis', {
                    requestId: 'aiEnhanceBtn'
                });

                // Parse the AI response
                try {
                    const jsonMatch = apiResponse.result.match(/\{[\s\S]*\}/);
                    if (jsonMatch) {
                        analysis = JSON.parse(jsonMatch[0]);
                        analysis.ai_backend = 'multi-api';
                        analysis.api_used = apiResponse.apiUsed;
                        analysis.response_time = apiResponse.responseTime;
                        analysis.analysis_timestamp = new Date().toISOString();
                        analysis.from_cache = apiResponse.fromCache || false;

                        const cacheInfo = apiResponse.fromCache ? ' (cached)' : '';
                        console.log(`✅ Bug analysis completed using ${multiAPIManager.apiConfigs[apiResponse.apiUsed].name}${cacheInfo}`);

                        // Track usage for analytics
                        trackAPIUsage(apiResponse, 'bug-analysis');
                    } else {
                        throw new Error('No JSON found in response');
                    }
                } catch (parseError) {
                    console.warn('Failed to parse AI response as JSON, using fallback analysis');
                    analysis = parseAIResponseToBugAnalysis(apiResponse.result, title, description);
                    analysis.api_used = apiResponse.apiUsed;
                    analysis.from_cache = apiResponse.fromCache || false;
                }
            } catch (error) {
                console.warn('Multi-API bug analysis failed, falling back to local AI:', error);

                // Show specific error message to user
                if (error.message.includes('cancelled')) {
                    showToast('Analysis cancelled by user', 'info');
                    return;
                } else if (error.message.includes('timeout')) {
                    showToast('Analysis timed out. Please try again.', 'warning');
                } else if (error.message.includes('rate limit')) {
                    showToast('Rate limit reached. Trying alternative AI...', 'warning');
                }

                analysis = await productionAI.analyzeBug(title, description);
            }
        } else {
            // Use Production AI System (Python backend with JavaScript fallback)
            analysis = await productionAI.analyzeBug(title, description);
        }

        // Auto-fill severity if not set
        const severitySelect = document.getElementById('severity');
        if (severitySelect && !severitySelect.value && analysis.predicted_severity) {
            severitySelect.value = analysis.predicted_severity;
        }

        // Auto-fill description if empty
        if (!description && analysis.suggested_description) {
            descriptionInput.value = analysis.suggested_description;
        }

        // Show AI insights
        displayAIInsights(analysis);

        // Show success message with API info
        const apiInfo = analysis.api_used ? ` using ${multiAPIManager.apiConfigs[analysis.api_used]?.name || 'AI'}` : '';
        showToast(`Bug report enhanced with AI insights${apiInfo}! 🤖`, 'success');

    } catch (error) {
        console.error('AI enhancement failed:', error);
        showToast('AI enhancement failed. Please try again.', 'error');
    } finally {
        aiEnhanceBtn.innerHTML = originalContent;
        aiEnhanceBtn.disabled = false;
    }
}

// Parse AI response to bug analysis when JSON parsing fails
function parseAIResponseToBugAnalysis(response, title, description) {
    const analysis = {
        predicted_severity: 'Medium',
        predicted_category: 'Functional',
        confidence_scores: { severity: 0.7, category: 0.7 },
        entities: { ui_components: [], actions: [], error_types: [], technologies: [] },
        sentiment_score: 0.5,
        urgency_score: 0.5,
        suggested_description: description || `Analysis of: ${title}`,
        recommendations: [],
        quality_assessment: { completeness: 0.7, clarity: 0.7, reproducibility: 0.7 },
        related_areas: [],
        impact_analysis: { user_impact: 'Medium', business_impact: 'Medium', technical_complexity: 'Medium' },
        ai_backend: 'multi-api-fallback',
        analysis_timestamp: new Date().toISOString()
    };

    const responseLower = response.toLowerCase();

    // Extract severity
    if (responseLower.includes('critical') || responseLower.includes('urgent')) {
        analysis.predicted_severity = 'Critical';
    } else if (responseLower.includes('high') || responseLower.includes('important')) {
        analysis.predicted_severity = 'High';
    } else if (responseLower.includes('low') || responseLower.includes('minor')) {
        analysis.predicted_severity = 'Low';
    }

    // Extract category
    if (responseLower.includes('ui') || responseLower.includes('interface') || responseLower.includes('design')) {
        analysis.predicted_category = 'UI/UX';
    } else if (responseLower.includes('performance') || responseLower.includes('slow') || responseLower.includes('speed')) {
        analysis.predicted_category = 'Performance';
    } else if (responseLower.includes('security') || responseLower.includes('auth') || responseLower.includes('permission')) {
        analysis.predicted_category = 'Security';
    }

    // Extract basic recommendations
    const lines = response.split('\n');
    for (const line of lines) {
        if (line.toLowerCase().includes('recommend') || line.toLowerCase().includes('suggest')) {
            analysis.recommendations.push({
                type: 'general',
                title: 'AI Recommendation',
                recommendation: line.trim(),
                confidence: 0.7
            });
        }
    }

    return analysis;
}

// Parse AI response to bug details when JSON parsing fails
function parseAIResponseToBugDetails(response, title) {
    const bugDetails = {
        description: `Detailed analysis of: ${title}`,
        severity: 'Medium',
        steps: 'Steps to reproduce this issue need to be determined.',
        actualResult: 'The system exhibits unexpected behavior.',
        expectedResult: 'The system should work as intended.',
        category: 'Functional',
        impact: 'Impact assessment needed',
        workaround: 'No workaround available',
        additionalInfo: '',
        aiGenerated: true,
        multiAPIMode: true,
        parsedFromText: true
    };

    const responseLower = response.toLowerCase();
    const lines = response.split('\n');

    // Extract severity
    if (responseLower.includes('critical') || responseLower.includes('urgent') || responseLower.includes('severe')) {
        bugDetails.severity = 'Critical';
    } else if (responseLower.includes('high') || responseLower.includes('important') || responseLower.includes('major')) {
        bugDetails.severity = 'High';
    } else if (responseLower.includes('low') || responseLower.includes('minor') || responseLower.includes('trivial')) {
        bugDetails.severity = 'Low';
    }

    // Extract category
    if (responseLower.includes('ui') || responseLower.includes('interface') || responseLower.includes('design') || responseLower.includes('visual')) {
        bugDetails.category = 'UI/UX';
    } else if (responseLower.includes('performance') || responseLower.includes('slow') || responseLower.includes('speed') || responseLower.includes('timeout')) {
        bugDetails.category = 'Performance';
    } else if (responseLower.includes('security') || responseLower.includes('auth') || responseLower.includes('permission') || responseLower.includes('vulnerability')) {
        bugDetails.category = 'Security';
    } else if (responseLower.includes('mobile') || responseLower.includes('responsive') || responseLower.includes('tablet')) {
        bugDetails.category = 'Mobile';
    } else if (responseLower.includes('integration') || responseLower.includes('api') || responseLower.includes('service')) {
        bugDetails.category = 'Integration';
    }

    // Extract description from first substantial paragraph
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.length > 50 && !trimmedLine.toLowerCase().includes('title:') && !trimmedLine.toLowerCase().includes('severity:')) {
            bugDetails.description = trimmedLine;
            break;
        }
    }

    // Extract steps if mentioned
    let stepsSection = false;
    const steps = [];
    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.toLowerCase().includes('steps') || trimmedLine.toLowerCase().includes('reproduce')) {
            stepsSection = true;
            continue;
        }
        if (stepsSection && trimmedLine) {
            if (trimmedLine.toLowerCase().includes('expected') || trimmedLine.toLowerCase().includes('actual')) {
                break;
            }
            steps.push(trimmedLine.replace(/^[-*\d.]\s*/, ''));
        }
    }
    if (steps.length > 0) {
        bugDetails.steps = steps.join('\n');
    }

    // Extract expected and actual results
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i].toLowerCase();
        if (line.includes('expected') && i + 1 < lines.length) {
            bugDetails.expectedResult = lines[i + 1].trim();
        }
        if (line.includes('actual') && i + 1 < lines.length) {
            bugDetails.actualResult = lines[i + 1].trim();
        }
    }

    return bugDetails;
}

// Display AI insights in the UI
function displayAIInsights(analysis) {
    // Create or update AI insights panel
    let insightsPanel = document.getElementById('aiInsightsPanel');
    if (!insightsPanel) {
        insightsPanel = document.createElement('div');
        insightsPanel.id = 'aiInsightsPanel';
        insightsPanel.className = 'alert alert-info mt-3';

        // Insert after the description field
        const descriptionGroup = descriptionInput.closest('.mb-3');
        descriptionGroup.parentNode.insertBefore(insightsPanel, descriptionGroup.nextSibling);
    }

    // Build entities display
    let entitiesHtml = '';
    if (analysis.entities) {
        Object.entries(analysis.entities).forEach(([type, items]) => {
            if (items && items.length > 0) {
                entitiesHtml += `<div class="mb-2">
                    <strong>${type.replace('_', ' ').toUpperCase()}:</strong>
                    ${items.map(item => `<span class="badge bg-secondary me-1">${item}</span>`).join('')}
                </div>`;
            }
        });
    }

    // Build recommendations display
    let recommendationsHtml = '';
    if (analysis.recommendations && analysis.recommendations.length > 0) {
        recommendationsHtml = `<div class="mt-2">
            <strong>AI Recommendations:</strong>
            <ul class="mb-0">
                ${analysis.recommendations.map(rec =>
                    `<li>${rec.recommendation} <small class="text-muted">(${(rec.confidence * 100).toFixed(0)}% confidence)</small></li>`
                ).join('')}
            </ul>
        </div>`;
    }

    insightsPanel.innerHTML = `
        <div class="d-flex align-items-center mb-2">
            <i class="bi bi-robot me-2"></i>
            <strong>AI Analysis Results</strong>
            <button type="button" class="btn-close ms-auto" onclick="this.closest('.alert').style.display='none'"></button>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div><strong>Predicted Severity:</strong> <span class="badge bg-warning">${analysis.predicted_severity}</span></div>
                <div><strong>Predicted Category:</strong> <span class="badge bg-info">${analysis.predicted_category}</span></div>
                <div><strong>Urgency Score:</strong> ${(analysis.urgency_score * 100).toFixed(1)}%</div>
            </div>
            <div class="col-md-6">
                <div><strong>Confidence Levels:</strong></div>
                <div class="progress mb-1" style="height: 8px;">
                    <div class="progress-bar" style="width: ${analysis.confidence_scores.severity * 100}%"></div>
                </div>
                <small>Severity: ${(analysis.confidence_scores.severity * 100).toFixed(1)}%</small>
            </div>
        </div>
        ${entitiesHtml ? `<div class="mt-2">${entitiesHtml}</div>` : ''}
        ${recommendationsHtml}
        <div class="mt-2">
            <small class="text-muted">
                <i class="bi bi-info-circle"></i> Analysis powered by intelligent AI assistant
            </small>
        </div>
    `;
}

// Advanced semantic analysis to deeply understand the bug context
function performSemanticAnalysis(title, description) {
    const fullText = (title + ' ' + description).toLowerCase();

    const context = {
        originalTitle: title,
        originalDescription: description,

        // Core issue identification
        primaryIssue: extractPrimaryIssue(title),
        specificComponent: extractSpecificComponent(title),
        userAction: extractUserAction(title),
        errorCondition: extractErrorCondition(title),

        // Detailed entity extraction
        uiElements: extractUIElements(title),
        dataElements: extractDataElements(title),
        workflows: extractWorkflows(title),
        constraints: extractConstraints(title),

        // Context understanding
        location: extractLocation(title),
        timing: extractTiming(title),
        conditions: extractConditions(title),

        // Technical details
        fileTypes: extractFileTypes(title),
        sizes: extractSizes(title),
        numbers: extractNumbers(title),

        // Behavioral patterns
        frequency: extractFrequency(title),
        triggers: extractTriggers(title),

        // Impact assessment
        severity: assessSeverity(title, description),
        scope: assessScope(title, description)
    };

    return context;
}

// Extract the primary issue from the title
function extractPrimaryIssue(title) {
    const issuePatterns = [
        { pattern: /not working|doesn't work|broken|fails?|error/i, issue: 'functionality_failure' },
        { pattern: /disabled|inactive|unresponsive/i, issue: 'element_disabled' },
        { pattern: /not displaying|not showing|missing|disappeared/i, issue: 'display_issue' },
        { pattern: /slow|lag|timeout|freeze/i, issue: 'performance_issue' },
        { pattern: /crash|hang|stuck/i, issue: 'system_failure' },
        { pattern: /incorrect|wrong|invalid/i, issue: 'data_issue' },
        { pattern: /can't|cannot|unable/i, issue: 'access_issue' },
        { pattern: /overlap|misaligned|layout/i, issue: 'ui_layout' },
        { pattern: /upload|download|save|load/i, issue: 'file_operation' }
    ];

    for (const { pattern, issue } of issuePatterns) {
        if (pattern.test(title)) {
            return issue;
        }
    }
    return 'general_issue';
}

// Extract specific UI component mentioned
function extractSpecificComponent(title) {
    const componentPatterns = [
        { pattern: /submit button|save button|login button|search button|(\w+)\s+button/i, type: 'button' },
        { pattern: /dropdown|select|combobox/i, type: 'dropdown' },
        { pattern: /form|input field|text field|(\w+)\s+field/i, type: 'form' },
        { pattern: /modal|dialog|popup/i, type: 'modal' },
        { pattern: /table|grid|list/i, type: 'table' },
        { pattern: /menu|navigation|nav/i, type: 'menu' },
        { pattern: /image|photo|picture/i, type: 'image' },
        { pattern: /file upload|upload/i, type: 'file_upload' },
        { pattern: /search|filter/i, type: 'search' },
        { pattern: /page|screen|view/i, type: 'page' }
    ];

    for (const { pattern, type } of componentPatterns) {
        const match = title.match(pattern);
        if (match) {
            return {
                type: type,
                name: match[1] || match[0],
                fullMatch: match[0]
            };
        }
    }
    return null;
}

// Extract user action from the title
function extractUserAction(title) {
    const actionPatterns = [
        { pattern: /clicking|click|pressed|press/i, action: 'click' },
        { pattern: /submitting|submit/i, action: 'submit' },
        { pattern: /uploading|upload/i, action: 'upload' },
        { pattern: /downloading|download/i, action: 'download' },
        { pattern: /searching|search/i, action: 'search' },
        { pattern: /filtering|filter/i, action: 'filter' },
        { pattern: /sorting|sort/i, action: 'sort' },
        { pattern: /selecting|select/i, action: 'select' },
        { pattern: /typing|entering|input/i, action: 'input' },
        { pattern: /scrolling|scroll/i, action: 'scroll' },
        { pattern: /hovering|hover/i, action: 'hover' },
        { pattern: /dragging|drag/i, action: 'drag' }
    ];

    for (const { pattern, action } of actionPatterns) {
        if (pattern.test(title)) {
            return action;
        }
    }
    return null;
}

// Extract error condition details
function extractErrorCondition(title) {
    const errorPatterns = [
        { pattern: /after (\w+)/i, condition: 'sequence' },
        { pattern: /when (\w+)/i, condition: 'trigger' },
        { pattern: /for (\w+)/i, condition: 'target' },
        { pattern: /with (\w+)/i, condition: 'context' },
        { pattern: /on (\w+)/i, condition: 'location' }
    ];

    const conditions = [];
    for (const { pattern, condition } of errorPatterns) {
        const match = title.match(pattern);
        if (match) {
            conditions.push({
                type: condition,
                value: match[1],
                fullMatch: match[0]
            });
        }
    }
    return conditions;
}

// Extract UI elements mentioned in the title
function extractUIElements(title) {
    const elements = [];
    const elementPatterns = [
        /button/i, /field/i, /dropdown/i, /menu/i, /form/i, /modal/i, /dialog/i,
        /table/i, /list/i, /grid/i, /image/i, /icon/i, /link/i, /tab/i, /panel/i
    ];

    elementPatterns.forEach(pattern => {
        if (pattern.test(title)) {
            elements.push(title.match(pattern)[0]);
        }
    });

    return elements;
}

// Extract data elements and types
function extractDataElements(title) {
    const dataPatterns = [
        { pattern: /file|document|pdf|image|photo|video/i, type: 'file' },
        { pattern: /email|address/i, type: 'email' },
        { pattern: /password|pwd/i, type: 'password' },
        { pattern: /name|title|text/i, type: 'text' },
        { pattern: /number|amount|quantity|count/i, type: 'number' },
        { pattern: /date|time/i, type: 'datetime' }
    ];

    const elements = [];
    dataPatterns.forEach(({ pattern, type }) => {
        const match = title.match(pattern);
        if (match) {
            elements.push({ type, value: match[0] });
        }
    });

    return elements;
}

// Extract workflow information
function extractWorkflows(title) {
    const workflowPatterns = [
        { pattern: /login|signin|authentication/i, workflow: 'authentication' },
        { pattern: /checkout|purchase|payment/i, workflow: 'checkout' },
        { pattern: /registration|signup|register/i, workflow: 'registration' },
        { pattern: /upload|import/i, workflow: 'file_upload' },
        { pattern: /search|find|lookup/i, workflow: 'search' },
        { pattern: /profile|account|settings/i, workflow: 'profile_management' }
    ];

    for (const { pattern, workflow } of workflowPatterns) {
        if (pattern.test(title)) {
            return workflow;
        }
    }
    return null;
}

// Extract constraints and limitations
function extractConstraints(title) {
    const constraints = [];

    // Size constraints
    const sizeMatch = title.match(/(\d+)\s*(mb|kb|gb|bytes?)/i);
    if (sizeMatch) {
        constraints.push({
            type: 'size',
            value: sizeMatch[1],
            unit: sizeMatch[2],
            fullMatch: sizeMatch[0]
        });
    }

    // Count constraints
    const countMatch = title.match(/(\d+)\s*(times?|clicks?|attempts?)/i);
    if (countMatch) {
        constraints.push({
            type: 'count',
            value: countMatch[1],
            unit: countMatch[2],
            fullMatch: countMatch[0]
        });
    }

    return constraints;
}

// Extract location information
function extractLocation(title) {
    const locationPatterns = [
        { pattern: /on (\w+\s+page)/i, type: 'page' },
        { pattern: /in (\w+\s+section)/i, type: 'section' },
        { pattern: /at (\w+)/i, type: 'position' },
        { pattern: /(\w+\s+page)/i, type: 'page' }
    ];

    for (const { pattern, type } of locationPatterns) {
        const match = title.match(pattern);
        if (match) {
            return {
                type: type,
                value: match[1] || match[0],
                fullMatch: match[0]
            };
        }
    }
    return null;
}

// Extract timing information
function extractTiming(title) {
    const timingPatterns = [
        { pattern: /after (\w+)/i, type: 'sequence' },
        { pattern: /during (\w+)/i, type: 'concurrent' },
        { pattern: /before (\w+)/i, type: 'prerequisite' },
        { pattern: /while (\w+)/i, type: 'concurrent' }
    ];

    for (const { pattern, type } of timingPatterns) {
        const match = title.match(pattern);
        if (match) {
            return {
                type: type,
                value: match[1],
                fullMatch: match[0]
            };
        }
    }
    return null;
}

// Extract general conditions
function extractConditions(title) {
    const conditionPatterns = [
        { pattern: /when (\w+)/i, type: 'trigger' },
        { pattern: /if (\w+)/i, type: 'conditional' },
        { pattern: /with (\w+)/i, type: 'context' },
        { pattern: /without (\w+)/i, type: 'absence' }
    ];

    const conditions = [];
    conditionPatterns.forEach(({ pattern, type }) => {
        const match = title.match(pattern);
        if (match) {
            conditions.push({
                type: type,
                value: match[1],
                fullMatch: match[0]
            });
        }
    });

    return conditions;
}

// Extract file types
function extractFileTypes(title) {
    const fileTypePattern = /(png|jpg|jpeg|gif|pdf|doc|docx|xls|xlsx|csv|txt|zip|mp4|avi|mp3)/gi;
    const matches = title.match(fileTypePattern);
    return matches ? matches.map(match => match.toLowerCase()) : [];
}

// Extract sizes and measurements
function extractSizes(title) {
    const sizePattern = /(\d+(?:\.\d+)?)\s*(mb|kb|gb|bytes?|px|%)/gi;
    const matches = [];
    let match;

    while ((match = sizePattern.exec(title)) !== null) {
        matches.push({
            value: parseFloat(match[1]),
            unit: match[2].toLowerCase(),
            fullMatch: match[0]
        });
    }

    return matches;
}

// Extract numbers and quantities
function extractNumbers(title) {
    const numberPattern = /(\d+)/g;
    const matches = title.match(numberPattern);
    return matches ? matches.map(num => parseInt(num)) : [];
}

// Extract frequency information
function extractFrequency(title) {
    const frequencyPatterns = [
        { pattern: /twice|two times/i, frequency: 2 },
        { pattern: /three times/i, frequency: 3 },
        { pattern: /multiple times/i, frequency: 'multiple' },
        { pattern: /once/i, frequency: 1 },
        { pattern: /repeatedly/i, frequency: 'repeated' }
    ];

    for (const { pattern, frequency } of frequencyPatterns) {
        if (pattern.test(title)) {
            return frequency;
        }
    }
    return null;
}

// Extract trigger information
function extractTriggers(title) {
    const triggerPatterns = [
        { pattern: /clicking|click/i, trigger: 'click' },
        { pattern: /submitting|submit/i, trigger: 'submit' },
        { pattern: /loading|load/i, trigger: 'load' },
        { pattern: /refreshing|refresh/i, trigger: 'refresh' },
        { pattern: /resizing|resize/i, trigger: 'resize' }
    ];

    const triggers = [];
    triggerPatterns.forEach(({ pattern, trigger }) => {
        if (pattern.test(title)) {
            triggers.push(trigger);
        }
    });

    return triggers;
}

// Assess severity based on keywords
function assessSeverity(title, description) {
    const combined = (title + ' ' + description).toLowerCase();

    if (/crash|hang|freeze|data loss|security|critical/i.test(combined)) {
        return 'Critical';
    } else if (/error|fail|broken|not working|high/i.test(combined)) {
        return 'High';
    } else if (/slow|lag|minor|cosmetic|low/i.test(combined)) {
        return 'Low';
    }
    return 'Medium';
}

// Assess scope of the issue
function assessScope(title, description) {
    const combined = (title + ' ' + description).toLowerCase();

    if (/all users|entire system|global/i.test(combined)) {
        return 'system_wide';
    } else if (/specific|particular|certain/i.test(combined)) {
        return 'specific_case';
    } else if (/page|section|component/i.test(combined)) {
        return 'component_level';
    }
    return 'feature_level';
}

// Generate reproduction test cases - highly specific to the reported issue
function generateReproductionTestCases(context) {
    const tests = [];
    const component = context.specificComponent;
    const action = context.userAction;
    const issue = context.primaryIssue;
    const title = context.originalTitle;

    // Primary reproduction test - exact scenario from title
    tests.push({
        id: 'REPRO_001',
        title: `Reproduce: ${title}`,
        description: `Exact reproduction of the reported issue: ${title}`,
        steps: generateSpecificReproductionSteps(context),
        expectedResult: generateExpectedResult(context),
        priority: context.severity,
        type: 'Reproduction'
    });

    // Variation tests based on specific context
    if (context.frequency && context.frequency > 1) {
        tests.push({
            id: 'REPRO_002',
            title: `Verify ${component?.name || 'element'} behavior with ${context.frequency} ${action}s`,
            description: `Test the specific frequency mentioned in the bug report`,
            steps: generateFrequencySpecificSteps(context),
            expectedResult: `${component?.name || 'Element'} should handle ${context.frequency} ${action}s correctly`,
            priority: 'High',
            type: 'Reproduction'
        });
    }

    // Constraint-specific tests
    if (context.constraints.length > 0) {
        context.constraints.forEach((constraint, index) => {
            tests.push({
                id: `REPRO_${String(tests.length + 1).padStart(3, '0')}`,
                title: `Test ${constraint.type} constraint: ${constraint.fullMatch}`,
                description: `Verify behavior with the specific ${constraint.type} constraint mentioned in the bug`,
                steps: generateConstraintSpecificSteps(context, constraint),
                expectedResult: generateConstraintExpectedResult(context, constraint),
                priority: 'High',
                type: 'Reproduction'
            });
        });
    }

    // Location-specific test
    if (context.location) {
        tests.push({
            id: `REPRO_${String(tests.length + 1).padStart(3, '0')}`,
            title: `Verify issue ${context.location.fullMatch}`,
            description: `Test the specific location mentioned in the bug report`,
            steps: generateLocationSpecificSteps(context),
            expectedResult: `Issue should be reproducible ${context.location.fullMatch}`,
            priority: 'High',
            type: 'Reproduction'
        });
    }

    return {
        category: 'Exact Reproduction',
        description: 'Test cases that reproduce the exact scenario described in the bug report',
        tests: tests,
        count: tests.length
    };
}

// Generate validation test cases - verify the fix works
function generateValidationTestCases(context) {
    const tests = [];
    const component = context.specificComponent;
    const action = context.userAction;

    // Primary validation test
    tests.push({
        id: 'VAL_001',
        title: `Validate ${component?.name || 'component'} works correctly after fix`,
        description: `Ensure the ${component?.name || 'component'} functions as expected after the issue is resolved`,
        steps: generateValidationSteps(context),
        expectedResult: generatePositiveExpectedResult(context),
        priority: 'Critical',
        type: 'Validation'
    });

    // Action-specific validation
    if (action) {
        tests.push({
            id: 'VAL_002',
            title: `Validate ${action} operation works correctly`,
            description: `Verify that ${action} operation on ${component?.name || 'the element'} functions properly`,
            steps: generateActionValidationSteps(context),
            expectedResult: `${action} operation completes successfully without issues`,
            priority: 'High',
            type: 'Validation'
        });
    }

    // Workflow validation if applicable
    if (context.workflows) {
        tests.push({
            id: 'VAL_003',
            title: `Validate ${context.workflows} workflow integrity`,
            description: `Ensure the entire ${context.workflows} workflow works correctly`,
            steps: generateWorkflowValidationSteps(context),
            expectedResult: `Complete ${context.workflows} workflow functions without errors`,
            priority: 'High',
            type: 'Validation'
        });
    }

    return {
        category: 'Fix Validation',
        description: 'Test cases to validate that the reported issue has been properly resolved',
        tests: tests,
        count: tests.length
    };
}

// Generate specific edge cases based on the bug context
function generateSpecificEdgeCases(context) {
    const tests = [];
    const component = context.specificComponent;

    // File type specific edge cases
    if (context.fileTypes.length > 0) {
        context.fileTypes.forEach(fileType => {
            tests.push({
                id: `EDGE_${String(tests.length + 1).padStart(3, '0')}`,
                title: `Test ${fileType.toUpperCase()} file handling edge cases`,
                description: `Test edge cases specific to ${fileType} files mentioned in the bug`,
                steps: generateFileTypeEdgeCaseSteps(context, fileType),
                expectedResult: `${fileType.toUpperCase()} files should be handled correctly in all edge cases`,
                priority: 'Medium',
                type: 'Edge Case'
            });
        });
    }

    // Size-specific edge cases
    if (context.sizes.length > 0) {
        context.sizes.forEach(size => {
            tests.push({
                id: `EDGE_${String(tests.length + 1).padStart(3, '0')}`,
                title: `Test ${size.fullMatch} boundary conditions`,
                description: `Test behavior at and around the ${size.fullMatch} limit mentioned in the bug`,
                steps: generateSizeEdgeCaseSteps(context, size),
                expectedResult: `System should handle files at ${size.fullMatch} boundary correctly`,
                priority: 'High',
                type: 'Edge Case'
            });
        });
    }

    // Timing-specific edge cases
    if (context.timing) {
        tests.push({
            id: `EDGE_${String(tests.length + 1).padStart(3, '0')}`,
            title: `Test timing edge case: ${context.timing.fullMatch}`,
            description: `Test the specific timing condition mentioned in the bug`,
            steps: generateTimingEdgeCaseSteps(context),
            expectedResult: `System should handle the timing condition ${context.timing.fullMatch} correctly`,
            priority: 'Medium',
            type: 'Edge Case'
        });
    }

    // If no specific edge cases found, generate component-specific ones
    if (tests.length === 0 && component) {
        tests.push({
            id: 'EDGE_001',
            title: `Test ${component.name} extreme usage scenarios`,
            description: `Test ${component.name} under extreme or unusual usage conditions`,
            steps: generateComponentEdgeCaseSteps(context),
            expectedResult: `${component.name} should handle extreme scenarios gracefully`,
            priority: 'Medium',
            type: 'Edge Case'
        });
    }

    return {
        category: 'Specific Edge Cases',
        description: 'Edge cases directly related to the specific issue reported',
        tests: tests,
        count: tests.length
    };
}

// Generate related scenarios based on the bug context
function generateRelatedScenarios(context) {
    const tests = [];
    const component = context.specificComponent;
    const workflow = context.workflows;

    // Related component scenarios
    if (component) {
        tests.push({
            id: 'REL_001',
            title: `Test ${component.name} in different contexts`,
            description: `Verify ${component.name} behavior in various usage scenarios`,
            steps: generateRelatedComponentSteps(context),
            expectedResult: `${component.name} should work consistently across different contexts`,
            priority: 'Medium',
            type: 'Related Scenario'
        });
    }

    // Workflow integration scenarios
    if (workflow) {
        tests.push({
            id: 'REL_002',
            title: `Test ${workflow} workflow variations`,
            description: `Test different paths through the ${workflow} workflow`,
            steps: generateWorkflowVariationSteps(context),
            expectedResult: `All ${workflow} workflow variations should function correctly`,
            priority: 'Medium',
            type: 'Related Scenario'
        });
    }

    // Cross-browser scenarios if UI-related
    if (context.uiElements.length > 0) {
        tests.push({
            id: 'REL_003',
            title: 'Test cross-browser compatibility for reported issue',
            description: 'Verify the issue and fix work consistently across different browsers',
            steps: generateCrossBrowserSteps(context),
            expectedResult: 'Functionality works consistently across all major browsers',
            priority: 'Medium',
            type: 'Related Scenario'
        });
    }

    return {
        category: 'Related Scenarios',
        description: 'Test scenarios related to the reported issue but in different contexts',
        tests: tests,
        count: tests.length
    };
}

// Generate targeted regression tests
function generateTargetedRegressionTests(context) {
    const tests = [];
    const component = context.specificComponent;

    // Core functionality regression
    tests.push({
        id: 'REG_001',
        title: `Regression test: Core ${component?.name || 'component'} functionality`,
        description: `Ensure fixing the reported issue doesn't break core ${component?.name || 'component'} functionality`,
        steps: generateRegressionSteps(context),
        expectedResult: `All core ${component?.name || 'component'} functions work as expected`,
        priority: 'Critical',
        type: 'Regression'
    });

    // Related feature regression
    if (context.workflows) {
        tests.push({
            id: 'REG_002',
            title: `Regression test: ${context.workflows} workflow integrity`,
            description: `Verify the fix doesn't impact the broader ${context.workflows} workflow`,
            steps: generateWorkflowRegressionSteps(context),
            expectedResult: `Complete ${context.workflows} workflow remains functional`,
            priority: 'High',
            type: 'Regression'
        });
    }

    return {
        category: 'Targeted Regression',
        description: 'Regression tests focused on areas likely to be affected by the fix',
        tests: tests,
        count: tests.length
    };
}

// Helper functions to generate specific test steps

function generateSpecificReproductionSteps(context) {
    const steps = [];
    const component = context.specificComponent;
    const action = context.userAction;
    const location = context.location;

    // Navigate to specific location if mentioned
    if (location) {
        steps.push(`Navigate to ${location.value}`);
    } else {
        steps.push('Navigate to the application');
    }

    // Locate the specific component
    if (component) {
        steps.push(`Locate the ${component.name}`);
    }

    // Perform the specific action mentioned
    if (action && component) {
        steps.push(`${action.charAt(0).toUpperCase() + action.slice(1)} the ${component.name}`);
    } else if (action) {
        steps.push(`Perform ${action} action`);
    }

    // Add frequency-specific steps
    if (context.frequency && context.frequency > 1) {
        steps.push(`Repeat the ${action || 'action'} ${context.frequency} times`);
    }

    // Add constraint-specific steps
    if (context.constraints.length > 0) {
        context.constraints.forEach(constraint => {
            if (constraint.type === 'size') {
                steps.push(`Use a file of exactly ${constraint.value}${constraint.unit}`);
            } else if (constraint.type === 'count') {
                steps.push(`Perform the action exactly ${constraint.value} ${constraint.unit}`);
            }
        });
    }

    steps.push('Observe the behavior and note any issues');

    return steps;
}

function generateExpectedResult(context) {
    const component = context.specificComponent;
    const action = context.userAction;
    const issue = context.primaryIssue;

    if (issue === 'functionality_failure') {
        return `${component?.name || 'Component'} should ${action || 'function'} correctly without errors`;
    } else if (issue === 'element_disabled') {
        return `${component?.name || 'Element'} should remain active and responsive`;
    } else if (issue === 'display_issue') {
        return `${component?.name || 'Element'} should display correctly and be visible`;
    } else if (issue === 'performance_issue') {
        return `${action || 'Operation'} should complete within acceptable time limits`;
    } else {
        return `${component?.name || 'Feature'} should work as designed without the reported issue`;
    }
}

function generateFrequencySpecificSteps(context) {
    const steps = [];
    const component = context.specificComponent;
    const action = context.userAction;
    const frequency = context.frequency;

    steps.push('Navigate to the relevant page/section');
    steps.push(`Locate the ${component?.name || 'element'}`);
    steps.push(`${action?.charAt(0).toUpperCase() + action?.slice(1) || 'Interact with'} the ${component?.name || 'element'} ${frequency} times in succession`);
    steps.push('Observe the behavior after each interaction');
    steps.push(`Verify the ${component?.name || 'element'} state after ${frequency} ${action}s`);

    return steps;
}

function generateConstraintSpecificSteps(context, constraint) {
    const steps = [];
    const component = context.specificComponent;

    steps.push('Navigate to the relevant section');

    if (constraint.type === 'size') {
        steps.push(`Prepare a file of exactly ${constraint.value}${constraint.unit}`);
        steps.push(`Attempt to ${context.userAction || 'upload'} the ${constraint.value}${constraint.unit} file`);
        steps.push('Monitor the system response');
    } else if (constraint.type === 'count') {
        steps.push(`Perform the action exactly ${constraint.value} ${constraint.unit}`);
        steps.push('Check system behavior at this specific count');
    }

    steps.push('Verify the system handles the constraint correctly');

    return steps;
}

function generateConstraintExpectedResult(context, constraint) {
    if (constraint.type === 'size') {
        return `System should handle ${constraint.value}${constraint.unit} files correctly according to specifications`;
    } else if (constraint.type === 'count') {
        return `System should respond appropriately after ${constraint.value} ${constraint.unit}`;
    }
    return 'System should handle the constraint as expected';
}

function generateLocationSpecificSteps(context) {
    const steps = [];
    const location = context.location;
    const component = context.specificComponent;
    const action = context.userAction;

    steps.push(`Navigate specifically to ${location.value}`);
    steps.push(`Verify you are ${location.fullMatch}`);

    if (component) {
        steps.push(`Locate the ${component.name} ${location.fullMatch}`);
    }

    if (action) {
        steps.push(`Perform ${action} action ${location.fullMatch}`);
    }

    steps.push('Observe if the issue occurs in this specific location');

    return steps;
}

function generateValidationSteps(context) {
    const steps = [];
    const component = context.specificComponent;
    const action = context.userAction;

    steps.push('Navigate to the application');

    if (component) {
        steps.push(`Locate the ${component.name}`);
        steps.push(`Verify ${component.name} is visible and accessible`);
    }

    if (action) {
        steps.push(`Perform ${action} operation`);
        steps.push(`Verify ${action} completes successfully`);
    }

    steps.push('Confirm no error messages appear');
    steps.push('Verify expected functionality works as designed');

    return steps;
}

function generatePositiveExpectedResult(context) {
    const component = context.specificComponent;
    const action = context.userAction;

    if (component && action) {
        return `${component.name} should respond correctly to ${action} operations without any issues`;
    } else if (component) {
        return `${component.name} should function properly and be fully operational`;
    } else if (action) {
        return `${action} operation should complete successfully without errors`;
    }
    return 'All functionality should work correctly without the previously reported issue';
}

// Additional helper functions for missing step generators

function generateActionValidationSteps(context) {
    const steps = [];
    const component = context.specificComponent;
    const action = context.userAction;

    steps.push('Navigate to the application');
    if (component) {
        steps.push(`Locate the ${component.name}`);
    }
    steps.push(`Perform ${action} operation`);
    steps.push(`Verify ${action} completes without errors`);
    steps.push('Check that the expected outcome occurs');
    steps.push('Confirm no unexpected side effects');

    return steps;
}

function generateWorkflowValidationSteps(context) {
    const steps = [];
    const workflow = context.workflows;

    steps.push('Navigate to the application');
    steps.push(`Start the ${workflow} workflow`);
    steps.push('Complete all steps in the workflow');
    steps.push('Verify each step functions correctly');
    steps.push('Confirm the workflow completes successfully');
    steps.push('Check that all expected outcomes are achieved');

    return steps;
}

function generateRelatedComponentSteps(context) {
    const steps = [];
    const component = context.specificComponent;

    steps.push('Navigate to different sections of the application');
    steps.push(`Test ${component?.name || 'component'} in various contexts`);
    steps.push('Verify consistent behavior across different pages');
    steps.push('Test with different user permissions if applicable');
    steps.push('Check behavior under different system states');

    return steps;
}

function generateWorkflowVariationSteps(context) {
    const steps = [];
    const workflow = context.workflows;

    steps.push('Navigate to the application');
    steps.push(`Test different entry points to ${workflow} workflow`);
    steps.push('Try alternative paths through the workflow');
    steps.push('Test with different data inputs');
    steps.push('Verify all workflow variations complete successfully');

    return steps;
}

function generateCrossBrowserSteps(context) {
    const steps = [];
    const component = context.specificComponent;

    steps.push('Open the application in Chrome browser');
    steps.push(`Test ${component?.name || 'functionality'} in Chrome`);
    steps.push('Open the application in Firefox browser');
    steps.push(`Test ${component?.name || 'functionality'} in Firefox`);
    steps.push('Open the application in Safari browser');
    steps.push(`Test ${component?.name || 'functionality'} in Safari`);
    steps.push('Compare behavior across all browsers');

    return steps;
}

function generateRegressionSteps(context) {
    const steps = [];
    const component = context.specificComponent;

    steps.push('Navigate to the application');
    steps.push(`Test all core ${component?.name || 'component'} functions`);
    steps.push('Verify basic operations work correctly');
    steps.push('Test integration with other components');
    steps.push('Confirm no existing functionality is broken');

    return steps;
}

function generateWorkflowRegressionSteps(context) {
    const steps = [];
    const workflow = context.workflows;

    steps.push('Navigate to the application');
    steps.push(`Execute complete ${workflow} workflow`);
    steps.push('Test all workflow branches and variations');
    steps.push('Verify integration points work correctly');
    steps.push('Confirm workflow performance is acceptable');

    return steps;
}

function generateFileTypeEdgeCaseSteps(context, fileType) {
    const steps = [];

    steps.push('Navigate to the file upload section');
    steps.push(`Prepare various ${fileType.toUpperCase()} files for testing`);
    steps.push(`Test with corrupted ${fileType.toUpperCase()} file`);
    steps.push(`Test with empty ${fileType.toUpperCase()} file`);
    steps.push(`Test with maximum size ${fileType.toUpperCase()} file`);
    steps.push(`Test with ${fileType.toUpperCase()} file with special characters in name`);
    steps.push('Verify system handles all edge cases appropriately');

    return steps;
}

function generateSizeEdgeCaseSteps(context, size) {
    const steps = [];

    steps.push('Navigate to the relevant section');
    steps.push(`Prepare file of exactly ${size.value}${size.unit}`);
    steps.push(`Test with file slightly under ${size.value}${size.unit}`);
    steps.push(`Test with file slightly over ${size.value}${size.unit}`);
    steps.push(`Test with file at exactly ${size.value}${size.unit}`);
    steps.push('Verify boundary behavior is correct');

    return steps;
}

function generateTimingEdgeCaseSteps(context) {
    const steps = [];
    const timing = context.timing;

    steps.push('Navigate to the application');
    steps.push(`Set up conditions for ${timing.fullMatch}`);
    steps.push(`Execute action ${timing.fullMatch}`);
    steps.push('Monitor system behavior during timing condition');
    steps.push('Verify system handles timing correctly');

    return steps;
}

function generateComponentEdgeCaseSteps(context) {
    const steps = [];
    const component = context.specificComponent;

    steps.push('Navigate to the application');
    steps.push(`Test ${component?.name || 'component'} with maximum data load`);
    steps.push(`Test ${component?.name || 'component'} with minimum data`);
    steps.push(`Test ${component?.name || 'component'} with invalid data`);
    steps.push(`Test ${component?.name || 'component'} under high system load`);
    steps.push('Verify graceful handling of extreme conditions');

    return steps;
}

// Generate functional test cases
function generateFunctionalTestCases(context) {
    const tests = [];
    const baseTitle = context.title || 'Feature';

    // Basic functionality tests
    tests.push({
        id: 'FUNC_001',
        title: `Verify ${baseTitle} - Basic Functionality`,
        description: `Test the core functionality of ${baseTitle}`,
        steps: [
            'Navigate to the application',
            'Locate the feature/component',
            'Perform the primary action',
            'Verify the expected outcome'
        ],
        expectedResult: 'Feature works as designed',
        priority: 'High',
        type: 'Positive'
    });

    // Action-based tests
    if (context.actions.length > 0) {
        context.actions.forEach((action, index) => {
            tests.push({
                id: `FUNC_${String(index + 2).padStart(3, '0')}`,
                title: `Verify ${action} functionality`,
                description: `Test ${action} operation in ${baseTitle}`,
                steps: [
                    'Navigate to the relevant section',
                    `Perform ${action} operation`,
                    'Verify the action completes successfully',
                    'Check for any side effects'
                ],
                expectedResult: `${action} operation completes successfully`,
                priority: 'High',
                type: 'Positive'
            });
        });
    }

    // Component-based tests
    if (context.components.length > 0) {
        context.components.forEach((component, index) => {
            tests.push({
                id: `FUNC_${String(tests.length + 1).padStart(3, '0')}`,
                title: `Verify ${component} component functionality`,
                description: `Test ${component} component behavior`,
                steps: [
                    `Navigate to ${component}`,
                    'Interact with the component',
                    'Verify component responds correctly',
                    'Test component state changes'
                ],
                expectedResult: `${component} component works correctly`,
                priority: 'Medium',
                type: 'Positive'
            });
        });
    }

    return {
        category: 'Functional Testing',
        description: 'Tests to verify that the software functions according to requirements',
        tests: tests,
        count: tests.length
    };
}

// Generate boundary test cases
function generateBoundaryTestCases(context) {
    const tests = [];
    const baseTitle = context.title || 'Feature';

    // Data boundary tests
    if (context.dataTypes.includes('text')) {
        tests.push({
            id: 'BOUND_001',
            title: 'Text Input - Minimum Length Boundary',
            description: 'Test text input with minimum allowed characters',
            steps: [
                'Navigate to text input field',
                'Enter minimum allowed characters (e.g., 1 character)',
                'Submit or validate the input',
                'Verify system accepts the input'
            ],
            expectedResult: 'System accepts minimum valid input',
            priority: 'Medium',
            type: 'Boundary'
        });

        tests.push({
            id: 'BOUND_002',
            title: 'Text Input - Maximum Length Boundary',
            description: 'Test text input with maximum allowed characters',
            steps: [
                'Navigate to text input field',
                'Enter maximum allowed characters',
                'Submit or validate the input',
                'Verify system accepts the input'
            ],
            expectedResult: 'System accepts maximum valid input',
            priority: 'Medium',
            type: 'Boundary'
        });
    }

    if (context.dataTypes.includes('number')) {
        tests.push({
            id: 'BOUND_003',
            title: 'Numeric Input - Minimum Value Boundary',
            description: 'Test numeric input with minimum allowed value',
            steps: [
                'Navigate to numeric input field',
                'Enter minimum allowed value',
                'Submit or validate the input',
                'Verify system accepts the value'
            ],
            expectedResult: 'System accepts minimum valid number',
            priority: 'Medium',
            type: 'Boundary'
        });

        tests.push({
            id: 'BOUND_004',
            title: 'Numeric Input - Maximum Value Boundary',
            description: 'Test numeric input with maximum allowed value',
            steps: [
                'Navigate to numeric input field',
                'Enter maximum allowed value',
                'Submit or validate the input',
                'Verify system accepts the value'
            ],
            expectedResult: 'System accepts maximum valid number',
            priority: 'Medium',
            type: 'Boundary'
        });
    }

    // File size boundaries
    if (context.dataTypes.includes('file')) {
        tests.push({
            id: 'BOUND_005',
            title: 'File Upload - Maximum Size Boundary',
            description: 'Test file upload with maximum allowed file size',
            steps: [
                'Navigate to file upload section',
                'Select a file at maximum allowed size',
                'Attempt to upload the file',
                'Verify upload succeeds'
            ],
            expectedResult: 'File uploads successfully at maximum size limit',
            priority: 'High',
            type: 'Boundary'
        });
    }

    return {
        category: 'Boundary Testing',
        description: 'Tests to verify system behavior at boundary conditions',
        tests: tests,
        count: tests.length
    };
}

// Generate negative test cases
function generateNegativeTestCases(context) {
    const tests = [];
    const baseTitle = context.title || 'Feature';

    // Invalid input tests
    tests.push({
        id: 'NEG_001',
        title: 'Invalid Input Handling',
        description: 'Test system behavior with invalid inputs',
        steps: [
            'Navigate to input fields',
            'Enter invalid data (special characters, wrong format)',
            'Attempt to submit',
            'Verify appropriate error handling'
        ],
        expectedResult: 'System displays appropriate error messages and prevents invalid submission',
        priority: 'High',
        type: 'Negative'
    });

    // Empty field tests
    tests.push({
        id: 'NEG_002',
        title: 'Required Field Validation',
        description: 'Test required field validation',
        steps: [
            'Navigate to form with required fields',
            'Leave required fields empty',
            'Attempt to submit',
            'Verify validation messages appear'
        ],
        expectedResult: 'System prevents submission and shows validation errors',
        priority: 'High',
        type: 'Negative'
    });

    // Unauthorized access tests
    if (context.userRoles.length > 0) {
        tests.push({
            id: 'NEG_003',
            title: 'Unauthorized Access Prevention',
            description: 'Test access control for unauthorized users',
            steps: [
                'Log in as unauthorized user or log out',
                'Attempt to access restricted functionality',
                'Verify access is denied',
                'Check for appropriate error messages'
            ],
            expectedResult: 'System denies access and shows appropriate error',
            priority: 'High',
            type: 'Negative'
        });
    }

    // SQL injection tests (if data-related)
    if (context.type === 'data' || context.dataTypes.length > 0) {
        tests.push({
            id: 'NEG_004',
            title: 'SQL Injection Prevention',
            description: 'Test system resistance to SQL injection attacks',
            steps: [
                'Navigate to input fields',
                'Enter SQL injection patterns (e.g., \'; DROP TABLE --)',
                'Submit the input',
                'Verify system handles malicious input safely'
            ],
            expectedResult: 'System prevents SQL injection and handles input safely',
            priority: 'Critical',
            type: 'Security'
        });
    }

    // Cross-site scripting tests
    tests.push({
        id: 'NEG_005',
        title: 'XSS Prevention',
        description: 'Test system resistance to cross-site scripting',
        steps: [
            'Navigate to input fields',
            'Enter script tags with JavaScript code',
            'Submit and view the output',
            'Verify scripts are not executed'
        ],
        expectedResult: 'System prevents script execution and sanitizes input',
        priority: 'Critical',
        type: 'Security'
    });

    return {
        category: 'Negative Testing',
        description: 'Tests to verify system behavior with invalid inputs and error conditions',
        tests: tests,
        count: tests.length
    };
}

// Generate performance test cases
function generatePerformanceTestCases(context) {
    const tests = [];

    // Load time tests
    tests.push({
        id: 'PERF_001',
        title: 'Page Load Performance',
        description: 'Test page loading time under normal conditions',
        steps: [
            'Clear browser cache',
            'Navigate to the application',
            'Measure page load time',
            'Verify load time meets requirements (< 3 seconds)'
        ],
        expectedResult: 'Page loads within acceptable time limits',
        priority: 'Medium',
        type: 'Performance'
    });

    // Stress testing
    tests.push({
        id: 'PERF_002',
        title: 'Concurrent User Load Test',
        description: 'Test system performance with multiple concurrent users',
        steps: [
            'Simulate multiple users accessing the system',
            'Monitor response times',
            'Check for system degradation',
            'Verify system remains stable'
        ],
        expectedResult: 'System maintains performance under load',
        priority: 'High',
        type: 'Performance'
    });

    // Memory usage tests
    tests.push({
        id: 'PERF_003',
        title: 'Memory Usage Monitoring',
        description: 'Test memory consumption during extended use',
        steps: [
            'Open browser developer tools',
            'Monitor memory usage baseline',
            'Perform various operations for extended period',
            'Check for memory leaks'
        ],
        expectedResult: 'No significant memory leaks detected',
        priority: 'Medium',
        type: 'Performance'
    });

    // Large data handling
    if (context.dataTypes.length > 0) {
        tests.push({
            id: 'PERF_004',
            title: 'Large Dataset Performance',
            description: 'Test system performance with large amounts of data',
            steps: [
                'Load large dataset into the system',
                'Perform search/filter operations',
                'Measure response times',
                'Verify system remains responsive'
            ],
            expectedResult: 'System handles large datasets efficiently',
            priority: 'Medium',
            type: 'Performance'
        });
    }

    return {
        category: 'Performance Testing',
        description: 'Tests to verify system performance under various conditions',
        tests: tests,
        count: tests.length
    };
}

// Generate security test cases
function generateSecurityTestCases(context) {
    const tests = [];

    // Authentication tests
    tests.push({
        id: 'SEC_001',
        title: 'Authentication Security',
        description: 'Test authentication mechanisms and security',
        steps: [
            'Attempt login with invalid credentials',
            'Test password complexity requirements',
            'Verify account lockout after failed attempts',
            'Test session timeout functionality'
        ],
        expectedResult: 'Authentication system is secure and robust',
        priority: 'Critical',
        type: 'Security'
    });

    // Data encryption tests
    tests.push({
        id: 'SEC_002',
        title: 'Data Transmission Security',
        description: 'Test data encryption during transmission',
        steps: [
            'Monitor network traffic during data submission',
            'Verify HTTPS is used for sensitive data',
            'Check for encrypted data transmission',
            'Verify no sensitive data in plain text'
        ],
        expectedResult: 'All sensitive data is encrypted during transmission',
        priority: 'Critical',
        type: 'Security'
    });

    // Input validation security
    tests.push({
        id: 'SEC_003',
        title: 'Input Validation Security',
        description: 'Test input validation for security vulnerabilities',
        steps: [
            'Test various malicious input patterns',
            'Attempt code injection attacks',
            'Test file upload security',
            'Verify input sanitization'
        ],
        expectedResult: 'System properly validates and sanitizes all inputs',
        priority: 'Critical',
        type: 'Security'
    });

    // Authorization tests
    if (context.userRoles.length > 0) {
        tests.push({
            id: 'SEC_004',
            title: 'Authorization and Access Control',
            description: 'Test user authorization and access control',
            steps: [
                'Test access with different user roles',
                'Attempt to access restricted resources',
                'Verify role-based permissions',
                'Test privilege escalation prevention'
            ],
            expectedResult: 'Access control is properly enforced for all user roles',
            priority: 'Critical',
            type: 'Security'
        });
    }

    return {
        category: 'Security Testing',
        description: 'Tests to verify system security and protection against threats',
        tests: tests,
        count: tests.length
    };
}

// Generate usability test cases
function generateUsabilityTestCases(context) {
    const tests = [];

    // User interface tests
    tests.push({
        id: 'USA_001',
        title: 'User Interface Intuitiveness',
        description: 'Test user interface design and intuitiveness',
        steps: [
            'Navigate through the application as a new user',
            'Attempt to complete common tasks',
            'Note any confusion or difficulties',
            'Verify UI elements are clearly labeled'
        ],
        expectedResult: 'Interface is intuitive and user-friendly',
        priority: 'Medium',
        type: 'Usability'
    });

    // Accessibility tests
    tests.push({
        id: 'USA_002',
        title: 'Accessibility Compliance',
        description: 'Test accessibility features and compliance',
        steps: [
            'Test keyboard navigation',
            'Verify screen reader compatibility',
            'Check color contrast ratios',
            'Test with accessibility tools'
        ],
        expectedResult: 'Application meets accessibility standards',
        priority: 'Medium',
        type: 'Usability'
    });

    // Error message clarity
    tests.push({
        id: 'USA_003',
        title: 'Error Message Clarity',
        description: 'Test clarity and helpfulness of error messages',
        steps: [
            'Trigger various error conditions',
            'Review error message content',
            'Verify messages are clear and actionable',
            'Test error message placement and visibility'
        ],
        expectedResult: 'Error messages are clear, helpful, and well-positioned',
        priority: 'Medium',
        type: 'Usability'
    });

    // Mobile usability (if mobile context detected)
    if (context.platforms.includes('mobile') || context.type === 'mobile') {
        tests.push({
            id: 'USA_004',
            title: 'Mobile Usability',
            description: 'Test usability on mobile devices',
            steps: [
                'Access application on mobile device',
                'Test touch interactions',
                'Verify responsive design',
                'Test mobile-specific features'
            ],
            expectedResult: 'Application is fully usable on mobile devices',
            priority: 'High',
            type: 'Usability'
        });
    }

    return {
        category: 'Usability Testing',
        description: 'Tests to verify user experience and interface design',
        tests: tests,
        count: tests.length
    };
}

// Generate compatibility test cases
function generateCompatibilityTestCases(context) {
    const tests = [];

    // Browser compatibility tests
    tests.push({
        id: 'COMP_001',
        title: 'Cross-Browser Compatibility',
        description: 'Test application functionality across different browsers',
        steps: [
            'Test in Chrome, Firefox, Safari, and Edge',
            'Verify all features work consistently',
            'Check for browser-specific issues',
            'Test JavaScript functionality'
        ],
        expectedResult: 'Application works consistently across all major browsers',
        priority: 'High',
        type: 'Compatibility'
    });

    // Operating system compatibility
    tests.push({
        id: 'COMP_002',
        title: 'Operating System Compatibility',
        description: 'Test application on different operating systems',
        steps: [
            'Test on Windows, macOS, and Linux',
            'Verify functionality is consistent',
            'Check for OS-specific issues',
            'Test file handling and downloads'
        ],
        expectedResult: 'Application works consistently across different operating systems',
        priority: 'Medium',
        type: 'Compatibility'
    });

    // Mobile device compatibility
    if (context.platforms.includes('mobile') || context.type === 'mobile') {
        tests.push({
            id: 'COMP_003',
            title: 'Mobile Device Compatibility',
            description: 'Test application on various mobile devices',
            steps: [
                'Test on different screen sizes',
                'Test on iOS and Android devices',
                'Verify touch interactions work properly',
                'Test responsive design breakpoints'
            ],
            expectedResult: 'Application is fully compatible with mobile devices',
            priority: 'High',
            type: 'Compatibility'
        });
    }

    // Screen resolution compatibility
    tests.push({
        id: 'COMP_004',
        title: 'Screen Resolution Compatibility',
        description: 'Test application at different screen resolutions',
        steps: [
            'Test at common resolutions (1920x1080, 1366x768, etc.)',
            'Test at very high and low resolutions',
            'Verify layout adapts properly',
            'Check for horizontal scrolling issues'
        ],
        expectedResult: 'Application displays correctly at all tested resolutions',
        priority: 'Medium',
        type: 'Compatibility'
    });

    return {
        category: 'Compatibility Testing',
        description: 'Tests to verify application works across different environments',
        tests: tests,
        count: tests.length
    };
}

// Generate regression test cases
function generateRegressionTestCases(context) {
    const tests = [];

    // Core functionality regression
    tests.push({
        id: 'REG_001',
        title: 'Core Functionality Regression',
        description: 'Verify core features still work after changes',
        steps: [
            'Test all primary user workflows',
            'Verify critical business functions',
            'Check data integrity',
            'Test user authentication and authorization'
        ],
        expectedResult: 'All core functionality works as expected',
        priority: 'Critical',
        type: 'Regression'
    });

    // UI regression tests
    tests.push({
        id: 'REG_002',
        title: 'User Interface Regression',
        description: 'Verify UI elements and layouts are not broken',
        steps: [
            'Check all pages and components',
            'Verify styling and layouts',
            'Test responsive design',
            'Check for visual inconsistencies'
        ],
        expectedResult: 'UI appears and functions correctly',
        priority: 'High',
        type: 'Regression'
    });

    // Data handling regression
    if (context.dataTypes.length > 0) {
        tests.push({
            id: 'REG_003',
            title: 'Data Handling Regression',
            description: 'Verify data operations work correctly',
            steps: [
                'Test data creation, reading, updating, deletion',
                'Verify data validation rules',
                'Check data persistence',
                'Test data import/export functions'
            ],
            expectedResult: 'All data operations work correctly',
            priority: 'Critical',
            type: 'Regression'
        });
    }

    // Integration regression
    tests.push({
        id: 'REG_004',
        title: 'Integration Points Regression',
        description: 'Verify external integrations still work',
        steps: [
            'Test API integrations',
            'Verify third-party service connections',
            'Check data synchronization',
            'Test authentication with external services'
        ],
        expectedResult: 'All integrations function properly',
        priority: 'High',
        type: 'Regression'
    });

    // Performance regression
    tests.push({
        id: 'REG_005',
        title: 'Performance Regression',
        description: 'Verify performance has not degraded',
        steps: [
            'Measure page load times',
            'Test response times for key operations',
            'Monitor memory usage',
            'Compare with baseline performance metrics'
        ],
        expectedResult: 'Performance meets or exceeds baseline metrics',
        priority: 'Medium',
        type: 'Regression'
    });

    return {
        category: 'Regression Testing',
        description: 'Tests to verify existing functionality still works after changes',
        tests: tests,
        count: tests.length
    };
}

// Display test cases in a comprehensive modal
function displayTestCasesModal(testCases) {
    // Create modal HTML
    const modalHTML = `
        <div class="modal fade" id="testCasesModal" tabindex="-1" aria-labelledby="testCasesModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="testCasesModalLabel">
                            <i class="bi bi-clipboard-check"></i> Advanced Test Cases Generated
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="test-cases-content">
                            ${generateTestCasesHTML(testCases)}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="exportTestCases()">
                            <i class="bi bi-download"></i> Export Test Cases
                        </button>
                        <button type="button" class="btn btn-success" onclick="copyTestCasesToClipboard()">
                            <i class="bi bi-clipboard"></i> Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('testCasesModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('testCasesModal'));
    modal.show();

    // Store test cases globally for export functions
    window.currentTestCases = testCases;
}

// Generate HTML content for test cases
function generateTestCasesHTML(testCases) {
    const metadata = testCases.metadata;
    const categories = testCases.categories;

    let html = `
        <div class="test-cases-header mb-4">
            <div class="row">
                <div class="col-md-8">
                    <h4 class="text-primary">${metadata.title}</h4>
                    <p class="text-muted">${metadata.description || 'No description provided'}</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="badge bg-success fs-6">
                        Total Test Cases: ${metadata.totalTestCases}
                    </div>
                    <div class="text-muted small mt-1">
                        Generated: ${new Date(metadata.generatedAt).toLocaleString()}
                    </div>
                </div>
            </div>
        </div>

        <div class="test-categories">
    `;

    // Generate content for each category
    Object.entries(categories).forEach(([key, category]) => {
        if (category.tests.length > 0) {
            html += `
                <div class="category-section mb-4">
                    <div class="category-header d-flex justify-content-between align-items-center mb-3">
                        <h5 class="category-title">
                            <i class="bi bi-${getCategoryIcon(key)}"></i>
                            ${category.category}
                            <span class="badge bg-secondary ms-2">${category.count}</span>
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse"
                                data-bs-target="#category-${key}" aria-expanded="true">
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                    <p class="category-description text-muted small">${category.description}</p>

                    <div class="collapse show" id="category-${key}">
                        <div class="test-cases-list">
                            ${category.tests.map(test => generateTestCaseHTML(test)).join('')}
                        </div>
                    </div>
                </div>
            `;
        }
    });

    html += '</div>';
    return html;
}

// Generate HTML for individual test case
function generateTestCaseHTML(test) {
    const priorityClass = getPriorityClass(test.priority);
    const typeClass = getTypeClass(test.type);

    return `
        <div class="test-case-card card mb-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <strong>${test.id}</strong> - ${test.title}
                    <div class="test-case-badges mt-1">
                        <span class="badge ${priorityClass}">${test.priority}</span>
                        <span class="badge ${typeClass}">${test.type}</span>
                    </div>
                </div>
                <button class="btn btn-sm btn-outline-secondary copy-test-btn"
                        onclick="copyTestCase('${test.id}')"
                        title="Copy this test case to clipboard"
                        data-bs-toggle="tooltip" data-bs-placement="top">
                    <i class="bi bi-clipboard"></i>
                </button>
            </div>
            <div class="card-body">
                <p class="test-description">${test.description}</p>

                <div class="test-steps mb-3">
                    <h6 class="fw-bold">Test Steps:</h6>
                    <ol class="test-steps-list">
                        ${test.steps.map(step => `<li>${step}</li>`).join('')}
                    </ol>
                </div>

                <div class="expected-result">
                    <h6 class="fw-bold">Expected Result:</h6>
                    <p class="text-success">${test.expectedResult}</p>
                </div>
            </div>
        </div>
    `;
}

// Helper functions for styling
function getCategoryIcon(category) {
    const icons = {
        functional: 'gear',
        boundary: 'rulers',
        negative: 'exclamation-triangle',
        performance: 'speedometer2',
        security: 'shield-check',
        usability: 'person-check',
        compatibility: 'device-hdd',
        regression: 'arrow-clockwise'
    };
    return icons[category] || 'clipboard-check';
}

function getPriorityClass(priority) {
    const classes = {
        'Critical': 'bg-danger',
        'High': 'bg-warning text-dark',
        'Medium': 'bg-info',
        'Low': 'bg-secondary'
    };
    return classes[priority] || 'bg-secondary';
}

function getTypeClass(type) {
    const classes = {
        'Positive': 'bg-success',
        'Negative': 'bg-danger',
        'Boundary': 'bg-warning text-dark',
        'Performance': 'bg-info',
        'Security': 'bg-dark',
        'Usability': 'bg-primary',
        'Compatibility': 'bg-secondary',
        'Regression': 'bg-dark'
    };
    return classes[type] || 'bg-light text-dark';
}

// Export test cases to various formats
function exportTestCases() {
    if (!window.currentTestCases) {
        showToast('No test cases to export!', 'error');
        return;
    }

    const testCases = window.currentTestCases;
    const exportOptions = `
        <div class="modal fade" id="exportTestCasesModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Export Test Cases</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Choose export format:</p>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportAsJSON()">
                                <i class="bi bi-filetype-json"></i> Export as JSON
                            </button>
                            <button class="btn btn-outline-success" onclick="exportAsCSV()">
                                <i class="bi bi-filetype-csv"></i> Export as CSV
                            </button>
                            <button class="btn btn-outline-info" onclick="exportAsPDF()">
                                <i class="bi bi-filetype-pdf"></i> Export as PDF
                            </button>
                            <button class="btn btn-outline-warning" onclick="exportAsExcel()">
                                <i class="bi bi-file-earmark-excel"></i> Export as Excel Format
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing export modal
    const existingExportModal = document.getElementById('exportTestCasesModal');
    if (existingExportModal) {
        existingExportModal.remove();
    }

    // Add export modal to DOM
    document.body.insertAdjacentHTML('beforeend', exportOptions);

    // Show export modal
    const exportModal = new bootstrap.Modal(document.getElementById('exportTestCasesModal'));
    exportModal.show();
}

// Export as JSON
function exportAsJSON() {
    const testCases = window.currentTestCases;
    const jsonData = JSON.stringify(testCases, null, 2);
    downloadFile(jsonData, `test-cases-${Date.now()}.json`, 'application/json');
    bootstrap.Modal.getInstance(document.getElementById('exportTestCasesModal')).hide();
    showToast('Test cases exported as JSON!', 'success');
}

// Export as CSV
function exportAsCSV() {
    const testCases = window.currentTestCases;
    let csvContent = 'Category,Test ID,Title,Description,Priority,Type,Steps,Expected Result\n';

    Object.entries(testCases.categories).forEach(([categoryKey, category]) => {
        category.tests.forEach(test => {
            const steps = test.steps.join(' | ');
            const row = [
                category.category,
                test.id,
                `"${test.title}"`,
                `"${test.description}"`,
                test.priority,
                test.type,
                `"${steps}"`,
                `"${test.expectedResult}"`
            ].join(',');
            csvContent += row + '\n';
        });
    });

    downloadFile(csvContent, `test-cases-${Date.now()}.csv`, 'text/csv');
    bootstrap.Modal.getInstance(document.getElementById('exportTestCasesModal')).hide();
    showToast('Test cases exported as CSV!', 'success');
}

// Export as PDF
function exportAsPDF() {
    const testCases = window.currentTestCases;
    const htmlContent = generatePDFContent(testCases);

    // Open in new window for printing/saving as PDF
    const newWindow = window.open('', '_blank');
    if (newWindow) {
        try {
            newWindow.document.documentElement.innerHTML = htmlContent;
            newWindow.document.close();
            setTimeout(() => {
                newWindow.print();
            }, 500);
        } catch (error) {
            console.warn('Modern approach failed, using fallback:', error);
            try {
                newWindow.document.write(htmlContent);
                newWindow.document.close();
                setTimeout(() => {
                    newWindow.print();
                }, 500);
            } catch (fallbackError) {
                console.error('PDF generation failed:', fallbackError);
                showToast('Failed to generate PDF. Please try again.', 'error');
                newWindow.close();
                return;
            }
        }
    }

    bootstrap.Modal.getInstance(document.getElementById('exportTestCasesModal')).hide();
    showToast('PDF export window opened!', 'success');
}

// Export as Excel format (CSV with Excel-friendly formatting)
function exportAsExcel() {
    const testCases = window.currentTestCases;
    let excelContent = 'Category\tTest ID\tTitle\tDescription\tPriority\tType\tSteps\tExpected Result\n';

    Object.entries(testCases.categories).forEach(([categoryKey, category]) => {
        category.tests.forEach(test => {
            const steps = test.steps.join('\n');
            const row = [
                category.category,
                test.id,
                test.title,
                test.description,
                test.priority,
                test.type,
                steps,
                test.expectedResult
            ].join('\t');
            excelContent += row + '\n';
        });
    });

    downloadFile(excelContent, `test-cases-${Date.now()}.xls`, 'application/vnd.ms-excel');
    bootstrap.Modal.getInstance(document.getElementById('exportTestCasesModal')).hide();
    showToast('Test cases exported as Excel format!', 'success');
}

// Generate PDF content
function generatePDFContent(testCases) {
    const metadata = testCases.metadata;

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Cases - ${metadata.title}</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    color: #333;
                }
                .header {
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 15px;
                    margin-bottom: 25px;
                }
                .test-title {
                    color: #007bff;
                    margin-bottom: 10px;
                    font-size: 28px;
                }
                .metadata {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 25px;
                }
                .category {
                    margin-bottom: 30px;
                    page-break-inside: avoid;
                }
                .category-title {
                    background: #007bff;
                    color: white;
                    padding: 10px 15px;
                    margin-bottom: 15px;
                    font-size: 18px;
                    border-radius: 5px;
                }
                .test-case {
                    border: 1px solid #ddd;
                    margin-bottom: 20px;
                    border-radius: 5px;
                    page-break-inside: avoid;
                }
                .test-header {
                    background: #f8f9fa;
                    padding: 10px 15px;
                    border-bottom: 1px solid #ddd;
                    font-weight: bold;
                }
                .test-body {
                    padding: 15px;
                }
                .test-steps {
                    margin: 10px 0;
                }
                .test-steps ol {
                    margin: 5px 0;
                    padding-left: 20px;
                }
                .expected-result {
                    background: #d4edda;
                    padding: 10px;
                    border-radius: 3px;
                    margin-top: 10px;
                }
                .priority-critical { color: #dc3545; font-weight: bold; }
                .priority-high { color: #fd7e14; font-weight: bold; }
                .priority-medium { color: #0dcaf0; font-weight: bold; }
                .priority-low { color: #6c757d; font-weight: bold; }
                .footer {
                    margin-top: 30px;
                    padding-top: 15px;
                    border-top: 1px solid #ddd;
                    font-size: 12px;
                    color: #6c757d;
                    text-align: center;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1 class="test-title">Test Cases: ${metadata.title}</h1>
                <div class="metadata">
                    <strong>Total Test Cases:</strong> ${metadata.totalTestCases} |
                    <strong>Generated:</strong> ${new Date(metadata.generatedAt).toLocaleString()}
                    ${metadata.description ? `<br><strong>Description:</strong> ${metadata.description}` : ''}
                </div>
            </div>

            ${Object.entries(testCases.categories).map(([key, category]) => {
                if (category.tests.length === 0) return '';

                return `
                    <div class="category">
                        <div class="category-title">
                            ${category.category} (${category.count} tests)
                        </div>
                        <p style="margin-bottom: 20px; color: #6c757d;">${category.description}</p>

                        ${category.tests.map(test => `
                            <div class="test-case">
                                <div class="test-header">
                                    ${test.id} - ${test.title}
                                    <span style="float: right;">
                                        <span class="priority-${test.priority.toLowerCase()}">${test.priority}</span> |
                                        ${test.type}
                                    </span>
                                </div>
                                <div class="test-body">
                                    <p><strong>Description:</strong> ${test.description}</p>

                                    <div class="test-steps">
                                        <strong>Test Steps:</strong>
                                        <ol>
                                            ${test.steps.map(step => `<li>${step}</li>`).join('')}
                                        </ol>
                                    </div>

                                    <div class="expected-result">
                                        <strong>Expected Result:</strong> ${test.expectedResult}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }).join('')}

            <div class="footer">
                <p>Generated by Bug Reporter App on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                <p>Advanced Test Case Generation System - Made with ❤️ by Ahmed Gamal</p>
            </div>
        </body>
        </html>
    `;
}

// Copy all test cases to clipboard with fallback methods
function copyTestCasesToClipboard() {
    if (!window.currentTestCases) {
        showToast('No test cases to copy!', 'error');
        return;
    }

    const testCases = window.currentTestCases;
    let textContent = `TEST CASES: ${testCases.metadata.title}\n`;
    textContent += `Generated: ${new Date(testCases.metadata.generatedAt).toLocaleString()}\n`;
    textContent += `Total Test Cases: ${testCases.metadata.totalTestCases}\n\n`;

    Object.entries(testCases.categories).forEach(([, category]) => {
        if (category.tests.length > 0) {
            textContent += `\n=== ${category.category.toUpperCase()} ===\n`;
            textContent += `${category.description}\n\n`;

            category.tests.forEach(test => {
                textContent += `${test.id} - ${test.title}\n`;
                textContent += `Priority: ${test.priority} | Type: ${test.type}\n`;
                textContent += `Description: ${test.description}\n\n`;
                textContent += `Test Steps:\n`;
                test.steps.forEach((step, index) => {
                    textContent += `${index + 1}. ${step}\n`;
                });
                textContent += `\nExpected Result: ${test.expectedResult}\n`;
                textContent += `${'='.repeat(50)}\n\n`;
            });
        }
    });

    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(textContent).then(() => {
            showToast('All test cases copied to clipboard!', 'success');
        }).catch(err => {
            console.warn('Modern clipboard API failed, trying fallback:', err);
            fallbackCopyToClipboard(textContent, 'All test cases copied to clipboard!');
        });
    } else {
        // Use fallback method for older browsers
        fallbackCopyToClipboard(textContent, 'All test cases copied to clipboard!');
    }
}

// Fallback clipboard copy method
function fallbackCopyToClipboard(text, successMessage) {
    try {
        // Create a temporary textarea element
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // Make it invisible but still selectable
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        textArea.style.opacity = '0';
        textArea.style.pointerEvents = 'none';

        document.body.appendChild(textArea);

        // Select and copy the text
        textArea.focus();
        textArea.select();
        textArea.setSelectionRange(0, 99999); // For mobile devices

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showToast(successMessage, 'success');
        } else {
            throw new Error('execCommand copy failed');
        }
    } catch (err) {
        console.error('Fallback copy failed:', err);
        // Final fallback - show the text in a modal for manual copying
        showTextForManualCopy(text);
    }
}

// Show text in a modal for manual copying when all else fails
function showTextForManualCopy(text) {
    const modalHTML = `
        <div class="modal fade" id="manualCopyModal" tabindex="-1" aria-labelledby="manualCopyModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="manualCopyModalLabel">
                            <i class="bi bi-clipboard"></i> Copy Test Cases Manually
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">Please select all text below and copy manually (Ctrl+C or Cmd+C):</p>
                        <textarea class="form-control" rows="20" readonly style="font-family: monospace; font-size: 12px;">${text.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</textarea>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="selectAllTextInModal()">
                            <i class="bi bi-check-all"></i> Select All
                        </button>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('manualCopyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('manualCopyModal'));
    modal.show();

    showToast('Automatic copy failed. Please copy manually from the modal.', 'warning');
}

// Helper function to select all text in the manual copy modal
function selectAllTextInModal() {
    const textarea = document.querySelector('#manualCopyModal textarea');
    if (textarea) {
        textarea.focus();
        textarea.select();
        textarea.setSelectionRange(0, 99999);
        showToast('Text selected! Press Ctrl+C (or Cmd+C) to copy.', 'info');
    }
}

// Copy individual test case to clipboard with fallback methods
function copyTestCase(testId) {
    if (!window.currentTestCases) {
        showToast('No test cases available!', 'error');
        return;
    }

    // Find the test case
    let foundTest = null;
    let foundCategory = null;

    Object.entries(window.currentTestCases.categories).forEach(([, category]) => {
        const test = category.tests.find(t => t.id === testId);
        if (test) {
            foundTest = test;
            foundCategory = category;
        }
    });

    if (!foundTest) {
        showToast('Test case not found!', 'error');
        return;
    }

    // Format test case for copying
    let textContent = `${foundTest.id} - ${foundTest.title}\n`;
    textContent += `Category: ${foundCategory.category}\n`;
    textContent += `Priority: ${foundTest.priority} | Type: ${foundTest.type}\n`;
    textContent += `Description: ${foundTest.description}\n\n`;
    textContent += `Test Steps:\n`;
    foundTest.steps.forEach((step, index) => {
        textContent += `${index + 1}. ${step}\n`;
    });
    textContent += `\nExpected Result: ${foundTest.expectedResult}\n`;

    // Visual feedback function
    const showCopySuccess = () => {
        showToast(`Test case ${testId} copied to clipboard!`, 'success');

        // Visual feedback on the copy button
        const copyBtn = document.querySelector(`button[onclick="copyTestCase('${testId}')"]`);
        if (copyBtn) {
            const originalContent = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="bi bi-check-lg text-success"></i>';
            copyBtn.classList.add('btn-success');
            copyBtn.classList.remove('btn-outline-secondary');

            setTimeout(() => {
                copyBtn.innerHTML = originalContent;
                copyBtn.classList.remove('btn-success');
                copyBtn.classList.add('btn-outline-secondary');
            }, 1500);
        }
    };

    // Try modern clipboard API first
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(textContent).then(() => {
            showCopySuccess();
        }).catch(err => {
            console.warn('Modern clipboard API failed for individual test case, trying fallback:', err);
            fallbackCopyToClipboard(textContent, `Test case ${testId} copied to clipboard!`);
        });
    } else {
        // Use fallback method for older browsers
        fallbackCopyToClipboard(textContent, `Test case ${testId} copied to clipboard!`);
    }
}

// Generate bug details based only on title analysis
function generateBugDetails() {
    const title = titleInput.value.trim();

    if (!title) {
        showToast('Please enter a bug title first!', 'warning');
        return;
    }

    // Show loading indicator
    const generateBtn = document.getElementById('generateBtn');
    const originalText = generateBtn.innerHTML;
    generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
    generateBtn.disabled = true;
    generateBtn.classList.add('btn-pulse');

    // Check if AI generation is enabled and API key exists
    const useAi = localStorage.getItem('useAiGeneration') === 'true';
    const apiKey = localStorage.getItem('apiKey');

    if (useAi && apiKey) {
        // Use AI to generate bug details based on title only
        generateBugDetailsWithAI(title, apiKey)
            .then(generatedDetails => {
                // Fill in the form fields with animation
                animateFillFormField(descriptionInput, generatedDetails.description);
                severityInput.value = generatedDetails.severity;
                animateFillFormField(stepsInput, generatedDetails.steps);
                animateFillFormField(actualResultInput, generatedDetails.actualResult);
                animateFillFormField(expectedResultInput, generatedDetails.expectedResult);

                // Show success message
                showToast('AI-powered bug details generated successfully!', 'info');
            })
            .catch(error => {
                console.error('Error generating bug details with AI:', error);
                // Fallback to local generation
                const generatedDetails = generateBugDetailsFromTitle(title);

                // Fill in the form fields with animation
                animateFillFormField(descriptionInput, generatedDetails.description);
                severityInput.value = generatedDetails.severity;
                animateFillFormField(stepsInput, generatedDetails.steps);
                animateFillFormField(actualResultInput, generatedDetails.actualResult);
                animateFillFormField(expectedResultInput, generatedDetails.expectedResult);

                // Show error message
                showToast('AI generation failed. Using local generation instead.', 'warning');
            })
            .finally(() => {
                // Reset button with animation
                generateBtn.classList.remove('btn-pulse');
                generateBtn.innerHTML = originalText;
                generateBtn.disabled = false;
            });
    } else {
        // Use enhanced local generation based on title
        const generatedDetails = generateBugDetailsFromTitle(title);

        // Fill in the form fields with animation
        animateFillFormField(descriptionInput, generatedDetails.description);
        severityInput.value = generatedDetails.severity;
        animateFillFormField(stepsInput, generatedDetails.steps);
        animateFillFormField(actualResultInput, generatedDetails.actualResult);
        animateFillFormField(expectedResultInput, generatedDetails.expectedResult);

        // Show message based on reason
        if (useAi && !apiKey) {
            showToast('No API key found. Please add your API key in Settings to use AI generation.', 'warning');
        } else {
            showToast('Bug details generated successfully!', 'info');
        }

        // Reset button
        generateBtn.classList.remove('btn-pulse');
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    }
}

// Animate filling form fields for a smoother experience
function animateFillFormField(element, text) {
    if (!element || !text) return;

    element.value = '';
    let index = 0;

    // Type effect animation
    const interval = setInterval(() => {
        element.value += text.charAt(index);
        index++;

        if (index >= text.length) {
            clearInterval(interval);
            element.classList.add('highlight-field');
            setTimeout(() => element.classList.remove('highlight-field'), 1000);
        }
    }, 10);
}

// Media analysis functions removed as part of title-only focus
// The app now relies solely on title-based bug generation
function generateBugDetailsFromTitleAndMedia(title) {
    // Now just redirects to title-only analysis
    return generateBugDetailsFromTitle(title);
}

// Determine severity based on title only
function determineSeverityFromTitleAndMedia(title) {
    // Function renamed for backward compatibility, but now only uses title
    const lowerTitle = title.toLowerCase();

    // Critical issues
    if (lowerTitle.includes('crash') || lowerTitle.includes('security') ||
        lowerTitle.includes('data loss') || lowerTitle.includes('breach')) {
        return 'Critical';
    }

    // High severity issues
    if (lowerTitle.includes('error') || lowerTitle.includes('fail') ||
        lowerTitle.includes('broken') || lowerTitle.includes('not working')) {
        return 'High';
    }

    // Default to Medium
    return 'Medium';
}

// Get context from title for better descriptions
function getIssueContextFromTitle(title) {
    const lowerTitle = title.toLowerCase();

    if (lowerTitle.includes('login') || lowerTitle.includes('auth')) {
        return "authentication or user access";
    } else if (lowerTitle.includes('save') || lowerTitle.includes('data')) {
        return "data saving or processing";
    } else if (lowerTitle.includes('display') || lowerTitle.includes('ui') || lowerTitle.includes('screen')) {
        return "the user interface or display elements";
    } else if (lowerTitle.includes('load') || lowerTitle.includes('performance')) {
        return "system performance or resource usage";
    } else {
        return "the functionality described in the title";
    }
}

// Get expected behavior based on title
function getExpectedBehaviorFromTitle(title) {
    const lowerTitle = title.toLowerCase();

    if (lowerTitle.includes('login') || lowerTitle.includes('auth')) {
        return "Users should be able to authenticate properly.";
    } else if (lowerTitle.includes('save') || lowerTitle.includes('data')) {
        return "Data should be saved correctly and securely.";
    } else if (lowerTitle.includes('display') || lowerTitle.includes('ui') || lowerTitle.includes('screen')) {
        return "The interface should display correctly on all supported devices and browsers.";
    } else if (lowerTitle.includes('load') || lowerTitle.includes('performance')) {
        return "The system should respond quickly and efficiently.";
    } else {
        return "The feature should work as designed without errors.";
    }
}
// Initialize file upload preview
const attachmentInput = document.getElementById('bugAttachments');
const attachmentPreview = document.getElementById('attachmentPreview');

if (attachmentInput) {
    attachmentInput.addEventListener('change', function() {
        // Clear previous previews
        attachmentPreview.innerHTML = '';

        // Create previews for each selected file
        for (const file of this.files) {
            const previewElement = document.createElement('div');
            previewElement.className = 'attachment-preview';

            if (file.type.startsWith('image/')) {
                // Create image preview
                const img = document.createElement('img');
                img.className = 'img-thumbnail';
                img.style.maxWidth = '100px';
                img.style.maxHeight = '100px';
                img.src = URL.createObjectURL(file);
                previewElement.appendChild(img);
            } else if (file.type.startsWith('video/')) {
                // Create video preview
                const video = document.createElement('video');
                video.className = 'img-thumbnail';
                video.style.maxWidth = '100px';
                video.style.maxHeight = '100px';
                video.src = URL.createObjectURL(file);
                video.muted = true;
                video.autoplay = false;
                video.controls = true;
                previewElement.appendChild(video);
            }

            // Add file name
            const fileName = document.createElement('div');
            fileName.className = 'small text-truncate';
            fileName.style.maxWidth = '100px';
            fileName.textContent = file.name;
            previewElement.appendChild(fileName);

            attachmentPreview.appendChild(previewElement);
        }
    });
}

// Initialize Summarize button
if (summarizeBenefitsBtn) {
    summarizeBenefitsBtn.addEventListener('click', summarizeBug);
}

// Summarize bug with AI-enhanced analysis
function summarizeBug() {
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();
    const severity = severityInput.value;
    const stepsToReproduce = stepsInput.value.trim();
    const actualResult = actualResultInput.value.trim();
    const expectedResult = expectedResultInput.value.trim();

    if (!title) {
        showToast('Please enter a bug title first!', 'warning');
        return;
    }

    // Show loading indicator
    const summarizeBtn = document.getElementById('summarizeBenefitsBtn');
    const originalText = summarizeBtn.innerHTML;
    summarizeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
    summarizeBtn.disabled = true;

    // Generate smart analysis based on bug details
    const bugType = analyzeBugType(title, description);
    const impactAnalysis = analyzeImpact(severity, bugType);
    const recommendedActions = generateRecommendations(bugType, severity);
    const timeEstimate = estimateFixTime(bugType, severity);

    // Generate benefits based on bug type and severity
    let benefits = '';

    // Business benefits based on severity
    if (severity === 'Critical') {
        benefits = `<strong>Business Benefits of Fixing:</strong><br>
1. Prevents potential data loss and system downtime<br>
2. Maintains user trust and prevents customer churn<br>
3. Avoids potential security vulnerabilities<br>
4. Reduces emergency support costs<br>
5. Protects company reputation and brand image`;
    } else if (severity === 'High') {
        benefits = `<strong>Business Benefits of Fixing:</strong><br>
1. Improves overall system stability and reliability<br>
2. Enhances user experience and satisfaction<br>
3. Reduces support ticket volume<br>
4. Increases user productivity and efficiency<br>
5. Prevents potential escalation to critical issues`;
    } else if (severity === 'Medium') {
        benefits = `<strong>Business Benefits of Fixing:</strong><br>
1. Enhances user interface consistency and usability<br>
2. Improves workflow efficiency for users<br>
3. Reduces minor frustrations that impact user satisfaction<br>
4. Demonstrates commitment to product quality<br>
5. Prevents accumulation of technical debt`;
    } else if (severity === 'Low') {
        benefits = `<strong>Business Benefits of Fixing:</strong><br>
1. Polishes the overall user experience<br>
2. Improves perception of product quality and attention to detail<br>
3. Enhances professional appearance of the application<br>
4. Contributes to consistent branding and messaging<br>
5. Addresses issues before they affect more users`;
    } else {
        benefits = `<strong>Business Benefits of Fixing:</strong><br>
1. Improves overall product quality and reliability<br>
2. Enhances user experience and satisfaction<br>
3. Reduces potential support costs<br>
4. Maintains competitive advantage in the market<br>
5. Demonstrates commitment to continuous improvement`;
    }

    // Create enhanced summary with all bug details and smart analysis with copy buttons
    const analysisId = Date.now(); // Unique ID for this analysis
    const summary = `
        <div class="bug-summary">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4 id="smartAnalysisTitle-${analysisId}">Smart Bug Analysis</h4>
                <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="smartAnalysisTitle-${analysisId}" title="Copy Analysis Title">
                    <i class="bi bi-clipboard"></i>
                </button>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>Title:</strong>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisTitle-${analysisId}" title="Copy Title">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisTitle-${analysisId}">${title}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>Severity:</strong>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisSeverity-${analysisId}" title="Copy Severity">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisSeverity-${analysisId}">${severity || 'Not specified'}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>Bug Type:</strong>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisBugType-${analysisId}" title="Copy Bug Type">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisBugType-${analysisId}">${bugType}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <strong>Description:</strong>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisDescription-${analysisId}" title="Copy Description">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisDescription-${analysisId}">${description || 'Not provided'}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Steps to Reproduce:</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisSteps-${analysisId}" title="Copy Steps">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisSteps-${analysisId}">${stepsToReproduce || 'Not provided'}</p>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Actual Result:</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisActual-${analysisId}" title="Copy Actual Result">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="analysisActual-${analysisId}">${actualResult || 'Not provided'}</p>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Expected Result:</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisExpected-${analysisId}" title="Copy Expected Result">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="analysisExpected-${analysisId}">${expectedResult || 'Not provided'}</p>
                </div>
            </div>

            <hr>
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Impact Analysis:</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisImpact-${analysisId}" title="Copy Impact Analysis">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisImpact-${analysisId}">${impactAnalysis}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Recommended Actions:</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisActions-${analysisId}" title="Copy Recommended Actions">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisActions-${analysisId}">${recommendedActions}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Estimated Fix Time:</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisTime-${analysisId}" title="Copy Time Estimate">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisTime-${analysisId}">${timeEstimate}</p>
            </div>

            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Benefits Analysis:</h5>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="analysisBenefits-${analysisId}" title="Copy Benefits Analysis">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <p id="analysisBenefits-${analysisId}">${benefits}</p>
            </div>

            <div class="mt-4 pt-3 border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Complete Smart Analysis</h5>
                    <button class="btn btn-outline-primary copy-btn" type="button" data-target="completeSmartAnalysis-${analysisId}" title="Copy Complete Analysis">
                        <i class="bi bi-clipboard"></i> Copy All
                    </button>
                </div>
                <div id="completeSmartAnalysis-${analysisId}" style="display: none;">
Smart Bug Analysis

Title: ${title}
Severity: ${severity || 'Not specified'}
Bug Type: ${bugType}
Description: ${description || 'Not provided'}

Steps to Reproduce:
${stepsToReproduce || 'Not provided'}

Actual Result:
${actualResult || 'Not provided'}

Expected Result:
${expectedResult || 'Not provided'}

Impact Analysis:
${impactAnalysis.replace(/<[^>]*>/g, '').trim()}

Recommended Actions:
${recommendedActions.replace(/<[^>]*>/g, '').trim()}

Estimated Fix Time:
${timeEstimate.replace(/<[^>]*>/g, '').trim()}

Benefits Analysis:
${benefits.replace(/<[^>]*>/g, '').trim()}
                </div>
            </div>
        </div>
    `;

    // Reset button
    summarizeBtn.innerHTML = originalText;
    summarizeBtn.disabled = false;

    // Display in modal
    const modalContent = document.getElementById('bugDetailsContent');
    modalContent.innerHTML = summary;

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('bugDetailsModal'));
    modal.show();

    // Initialize copy buttons for the smart analysis modal
    setTimeout(() => {
        initializeCopyButtons();
    }, 100);

    // Show success message
    showToast('Smart bug analysis generated!', 'info');
}

// Get priority based on severity
function getPriorityFromSeverity(severity) {
    switch(severity) {
        case 'Critical':
            return 'High';
        case 'High':
            return 'High';
        case 'Medium':
            return 'Medium';
        case 'Low':
            return 'Low';
        default:
            return 'Medium';
    }
}

// Enhanced smart bug analysis functions
function analyzeBugType(title, description) {
    // Enhanced bug types with weights and more comprehensive keywords
    const bugTypes = [
        {
            type: 'UI/UX Issue',
            keywords: ['button', 'click', 'ui', 'interface', 'display', 'layout', 'design', 'style', 'color', 'font', 'css', 'responsive', 'mobile view', 'alignment'],
            weight: 1.0
        },
        {
            type: 'Performance Issue',
            keywords: ['slow', 'performance', 'lag', 'freeze', 'hang', 'timeout', 'memory', 'speed', 'loading', 'delay', 'optimization', 'cpu'],
            weight: 1.2
        },
        {
            type: 'Functional Issue',
            keywords: ['not working', 'broken', 'error', 'exception', 'fail', 'incorrect', 'wrong', 'malfunction', 'defect', 'bug', 'missing'],
            weight: 1.1
        },
        {
            type: 'Security Issue',
            keywords: ['security', 'vulnerability', 'exploit', 'hack', 'authentication', 'authorization', 'permission', 'login', 'password', 'token', 'encryption'],
            weight: 1.5
        },
        {
            type: 'Data Issue',
            keywords: ['data', 'database', 'save', 'load', 'corrupt', 'missing', 'incorrect data', 'backup', 'restore', 'export', 'import', 'sync'],
            weight: 1.3
        },
        {
            type: 'Compatibility Issue',
            keywords: ['browser', 'device', 'mobile', 'desktop', 'compatibility', 'responsive', 'chrome', 'firefox', 'safari', 'edge', 'ios', 'android'],
            weight: 1.1
        },
        {
            type: 'Integration Issue',
            keywords: ['api', 'integration', 'third-party', 'service', 'connection', 'endpoint', 'webhook', 'external', 'plugin'],
            weight: 1.2
        },
        {
            type: 'Crash Issue',
            keywords: ['crash', 'exit', 'terminate', 'close', 'shutdown', 'fatal', 'exception', 'abort'],
            weight: 1.4
        },
        {
            type: 'Network Issue',
            keywords: ['network', 'connection', 'internet', 'offline', 'timeout', 'request failed', 'server', 'connectivity'],
            weight: 1.2
        },
        {
            type: 'Validation Issue',
            keywords: ['validation', 'form', 'input', 'required', 'format', 'email', 'phone', 'field'],
            weight: 1.0
        }
    ];

    // Combine title and description for analysis
    const text = (title + ' ' + (description || '')).toLowerCase();

    // Score each bug type based on keyword matches and weights
    const scores = bugTypes.map(bugType => {
        let score = 0;
        let matchCount = 0;

        for (const keyword of bugType.keywords) {
            if (text.includes(keyword)) {
                matchCount++;
                // Give more weight to exact matches and longer keywords
                const keywordWeight = keyword.length > 5 ? 2 : 1;
                score += keywordWeight * bugType.weight;
            }
        }

        // Bonus for multiple keyword matches
        if (matchCount > 1) {
            score += matchCount * 0.5;
        }

        return { type: bugType.type, score, matchCount };
    });

    // Find the highest scoring bug type
    const bestMatch = scores.reduce((best, current) =>
        current.score > best.score ? current : best,
        { type: 'General Issue', score: 0, matchCount: 0 }
    );

    // Return the best match if it has a reasonable score, otherwise return General Issue
    return bestMatch.score > 0 ? bestMatch.type : 'General Issue';
}

function analyzeImpact(severity, bugType) {
    let impact = '';

    if (severity === 'Critical') {
        impact = 'This issue has a <strong>severe impact</strong> on the system. It may cause data loss, security breaches, or complete system unavailability.';
    } else if (severity === 'High') {
        impact = 'This issue has a <strong>significant impact</strong> on the system. It affects core functionality and may cause substantial user frustration.';
    } else if (severity === 'Medium') {
        impact = 'This issue has a <strong>moderate impact</strong> on the system. It affects some functionality but has workarounds available.';
    } else if (severity === 'Low') {
        impact = 'This issue has a <strong>minor impact</strong> on the system. It causes minimal disruption and is primarily cosmetic.';
    } else {
        impact = 'Impact level cannot be determined without severity information.';
    }

    // Add specific impact based on bug type
    if (bugType === 'Security Issue') {
        impact += ' Security issues should be prioritized as they may lead to data breaches or unauthorized access.';
    } else if (bugType === 'Performance Issue') {
        impact += ' Performance issues can lead to user frustration and abandonment if not addressed promptly.';
    } else if (bugType === 'Crash Issue') {
        impact += ' Crash issues severely impact user experience and may lead to data loss if not properly handled.';
    } else if (bugType === 'Data Issue') {
        impact += ' Data issues may compromise data integrity and lead to incorrect business decisions or user actions.';
    }

    return impact;
}

function generateRecommendations(bugType, severity) {
    let recommendations = '';

    // Base recommendations on bug type
    if (bugType === 'UI/UX Issue') {
        recommendations = '1. Review design specifications<br>2. Conduct usability testing<br>3. Ensure consistent styling across the application';
    } else if (bugType === 'Performance Issue') {
        recommendations = '1. Profile the application to identify bottlenecks<br>2. Optimize database queries<br>3. Consider caching strategies<br>4. Review resource-intensive operations';
    } else if (bugType === 'Functional Issue') {
        recommendations = '1. Review business requirements<br>2. Add comprehensive unit tests<br>3. Implement proper error handling<br>4. Consider edge cases';
    } else if (bugType === 'Security Issue') {
        recommendations = '1. Conduct security audit<br>2. Implement proper input validation<br>3. Review authentication and authorization mechanisms<br>4. Follow security best practices';
    } else if (bugType === 'Data Issue') {
        recommendations = '1. Validate data integrity<br>2. Implement data validation rules<br>3. Add database constraints<br>4. Consider data recovery mechanisms';
    } else if (bugType === 'Compatibility Issue') {
        recommendations = '1. Test across multiple browsers/devices<br>2. Implement responsive design principles<br>3. Use feature detection instead of browser detection<br>4. Consider progressive enhancement';
    } else if (bugType === 'Integration Issue') {
        recommendations = '1. Review API documentation<br>2. Implement proper error handling for external services<br>3. Add retry mechanisms for transient failures<br>4. Consider circuit breaker patterns';
    } else if (bugType === 'Crash Issue') {
        recommendations = '1. Implement proper exception handling<br>2. Add crash reporting<br>3. Review memory management<br>4. Implement auto-save features to prevent data loss';
    } else {
        recommendations = '1. Conduct thorough testing<br>2. Review related code areas<br>3. Implement proper logging<br>4. Consider adding automated tests';
    }

    // Add priority based on severity
    if (severity === 'Critical' || severity === 'High') {
        recommendations += '<br><br><strong>Priority:</strong> Address immediately to prevent significant user impact.';
    } else if (severity === 'Medium') {
        recommendations += '<br><br><strong>Priority:</strong> Schedule for the next release cycle.';
    } else if (severity === 'Low') {
        recommendations += '<br><br><strong>Priority:</strong> Address when resources are available.';
    }

    return recommendations;
}

function estimateFixTime(bugType, severity) {
    let baseTime = 0;

    // Base time estimate on bug type
    if (bugType === 'UI/UX Issue') {
        baseTime = 4; // hours
    } else if (bugType === 'Performance Issue') {
        baseTime = 8;
    } else if (bugType === 'Functional Issue') {
        baseTime = 6;
    } else if (bugType === 'Security Issue') {
        baseTime = 10;
    } else if (bugType === 'Data Issue') {
        baseTime = 8;
    } else if (bugType === 'Compatibility Issue') {
        baseTime = 6;
    } else if (bugType === 'Integration Issue') {
        baseTime = 8;
    } else if (bugType === 'Crash Issue') {
        baseTime = 12;
    } else {
        baseTime = 6;
    }

    // Adjust based on severity
    if (severity === 'Critical') {
        baseTime *= 1.5; // More complex issues
    } else if (severity === 'Low') {
        baseTime *= 0.7; // Simpler issues
    }

    // Format the estimate
    const minTime = Math.round(baseTime * 0.7);
    const maxTime = Math.round(baseTime * 1.3);

    return `Approximately ${minTime}-${maxTime} hours, depending on complexity and developer familiarity with the codebase.`;
}

// Generate bug details from title
function generateBugDetailsFromTitle(title) {
    // Common bug patterns and their templates
    const bugPatterns = [
        { keywords: ['crash', 'freeze', 'hang'], severity: 'Critical', template: 'Application crashes' },
        { keywords: ['slow', 'performance', 'lag'], severity: 'High', template: 'Performance issue' },
        { keywords: ['button', 'click', 'ui'], severity: 'Medium', template: 'UI element issue' },
        { keywords: ['typo', 'spelling', 'text'], severity: 'Low', template: 'Text issue' },
        { keywords: ['login', 'auth', 'password'], severity: 'High', template: 'Authentication issue' },
        { keywords: ['data', 'save', 'load'], severity: 'High', template: 'Data handling issue' },
        { keywords: ['display', 'render', 'show'], severity: 'Medium', template: 'Display issue' },
        { keywords: ['error', 'exception', 'fail'], severity: 'High', template: 'Error handling issue' }
    ];

    // Default values
    let description = `Issue with ${title}`;
    let severity = 'Medium';
    let template = 'General issue';

    // Find matching pattern
    for (const pattern of bugPatterns) {
        for (const keyword of pattern.keywords) {
            if (title.toLowerCase().includes(keyword)) {
                severity = pattern.severity;
                template = pattern.template;
                break;
            }
        }
    }

    // Generate description based on title and template
    description = `${template} detected: ${title}. This issue needs to be investigated to determine the root cause and potential impact on users.`;

    // Generate preconditions based on bug type
    let preconditions = '';

    // Set specific preconditions based on the detected pattern
    if (title.toLowerCase().includes('crash') || title.toLowerCase().includes('freeze') || title.toLowerCase().includes('hang')) {
        preconditions = `1. The application must be running on a supported operating system version.\n2. System must have sufficient memory and CPU resources.\n3. All required system dependencies must be installed.\n4. Application must not be in the middle of an update process.`;
    } else if (title.toLowerCase().includes('slow') || title.toLowerCase().includes('performance') || title.toLowerCase().includes('lag')) {
        preconditions = `1. The system must meet minimum hardware requirements.\n2. No other resource-intensive applications should be running simultaneously.\n3. Network connection must be stable (if applicable).\n4. Database should not be experiencing high load (if applicable).`;
    } else if (title.toLowerCase().includes('button') || title.toLowerCase().includes('click') || title.toLowerCase().includes('ui')) {
        preconditions = `1. The user interface must be fully loaded.\n2. User must be logged in with appropriate permissions.\n3. The screen resolution must be supported.\n4. The browser/application window must not be minimized.`;
    } else if (title.toLowerCase().includes('login') || title.toLowerCase().includes('auth') || title.toLowerCase().includes('password')) {
        preconditions = `1. User account must exist in the system.\n2. Authentication services must be online and accessible.\n3. User must not be locked out of their account.\n4. Password policies must be properly configured.`;
    } else if (title.toLowerCase().includes('data') || title.toLowerCase().includes('save') || title.toLowerCase().includes('load')) {
        preconditions = `1. Database connection must be established and stable.\n2. User must have appropriate data access permissions.\n3. Storage space must be sufficient.\n4. Data format must be compatible with the system.`;
    } else {
        // Default preconditions for other types of bugs
        preconditions = `1. The application must be in a stable state with all dependencies properly loaded.\n2. User must have appropriate permissions to access the affected functionality.\n3. System environment variables must be correctly configured.\n4. Required services must be running.`;
    }

    // Generate steps to reproduce
    const steps = `1. Navigate to the affected area\n2. Attempt to reproduce the "${title}" issue\n3. Observe the unexpected behavior\n4. Try alternative approaches to verify consistency of the issue`;

    // Generate actual and expected results
    const actualResult = `When performing the action related to "${title}", the system behaves unexpectedly or incorrectly.`;
    const expectedResult = `The system should handle the "${title}" action correctly and provide appropriate feedback to the user.`;

    return {
        description: description,
        severity: severity,
        preconditions: preconditions,
        steps: steps,
        actualResult: actualResult,
        expectedResult: expectedResult
    };
}

// View bug details
function viewBugDetails(id) {
    const bugs = getBugsFromStorage();
    const bug = bugs.find(bug => bug.id === id);

    if (bug) {
        const modalContent = document.getElementById('bugDetailsContent');
        const dateCreated = new Date(bug.dateCreated).toLocaleString();

        const severityClass = `badge-${bug.severity.toLowerCase()}`;

        // Generate attachments HTML if any
        let attachmentsHtml = '';
        if (bug.attachments && bug.attachments.length > 0) {
            attachmentsHtml = `
                <div class="mb-3">
                    <h5>Attachments</h5>
                    <div class="d-flex flex-wrap gap-2">
            `;

            for (const attachment of bug.attachments) {
                if (attachment.type.startsWith('image/')) {
                    attachmentsHtml += `
                        <div class="attachment-item">
                            <img src="${attachment.data}" class="img-thumbnail" style="max-width: 150px; max-height: 150px;" alt="${attachment.name}">
                            <div class="small text-truncate" style="max-width: 150px;">${attachment.name}</div>
                        </div>
                    `;
                } else if (attachment.type.startsWith('video/')) {
                    attachmentsHtml += `
                        <div class="attachment-item">
                            <video src="${attachment.data}" class="img-thumbnail" style="max-width: 150px; max-height: 150px;" controls></video>
                            <div class="small text-truncate" style="max-width: 150px;">${attachment.name}</div>
                        </div>
                    `;
                } else {
                    attachmentsHtml += `
                        <div class="attachment-item">
                            <div class="file-icon p-3 bg-light rounded">
                                <i class="bi bi-file-earmark fs-1"></i>
                            </div>
                            <div class="small text-truncate" style="max-width: 150px;">${attachment.name}</div>
                        </div>
                    `;
                }
            }

            attachmentsHtml += `
                    </div>
                </div>
            `;
        }

        modalContent.innerHTML = `
            <div class="bug-details">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 id="bugTitle-${bug.id}">${bug.title}</h3>
                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugTitle-${bug.id}" title="Copy Title">
                        <i class="bi bi-clipboard"></i>
                    </button>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <span class="badge ${severityClass}" id="bugSeverity-${bug.id}">${bug.severity}</span>
                            <small class="text-muted ms-2">Created: ${dateCreated}</small>
                        </div>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugSeverity-${bug.id}" title="Copy Severity">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Description</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugDescription-${bug.id}" title="Copy Description">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugDescription-${bug.id}">${bug.description}</p>
                </div>
                ${bug.bugType ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Bug Type</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugType-${bug.id}" title="Copy Bug Type">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugType-${bug.id}">${bug.bugType}</p>
                </div>` : ''}
                ${bug.preconditions ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Preconditions</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugPreconditions-${bug.id}" title="Copy Preconditions">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugPreconditions-${bug.id}">${bug.preconditions.replace(/\n/g, '<br>')}</p>
                </div>` : ''}
                ${bug.stepsToReproduce ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Steps to Reproduce</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugSteps-${bug.id}" title="Copy Steps to Reproduce">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugSteps-${bug.id}">${bug.stepsToReproduce.replace(/\n/g, '<br>')}</p>
                </div>` : ''}
                ${bug.actualResult ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Actual Result</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugActualResult-${bug.id}" title="Copy Actual Result">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugActualResult-${bug.id}">${bug.actualResult.replace(/\n/g, '<br>')}</p>
                </div>` : ''}
                ${bug.expectedResult ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Expected Result</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugExpectedResult-${bug.id}" title="Copy Expected Result">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <p id="bugExpectedResult-${bug.id}">${bug.expectedResult.replace(/\n/g, '<br>')}</p>
                </div>` : ''}
                ${bug.environment ? `
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Environment</h5>
                        <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="bugEnvironment-${bug.id}" title="Copy Environment Info">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <div id="bugEnvironment-${bug.id}">
                        ${bug.environment.browser ? `<p><strong>Browser:</strong> ${bug.environment.browser}</p>` : ''}
                        ${bug.environment.os ? `<p><strong>OS:</strong> ${bug.environment.os}</p>` : ''}
                        ${bug.environment.screenResolution ? `<p><strong>Screen Resolution:</strong> ${bug.environment.screenResolution}</p>` : ''}
                        ${bug.environment.userAgent ? `<p><strong>User Agent:</strong> ${bug.environment.userAgent}</p>` : ''}
                    </div>
                </div>` : ''}
                ${attachmentsHtml}
                <div class="mt-4 pt-3 border-top">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5>Complete Bug Report</h5>
                        <button class="btn btn-outline-primary copy-btn" type="button" data-target="completeBugReport-${bug.id}" title="Copy Complete Bug Report">
                            <i class="bi bi-clipboard"></i> Copy All
                        </button>
                    </div>
                    <div id="completeBugReport-${bug.id}" style="display: none;">
Title: ${bug.title}
Severity: ${bug.severity}
Description: ${bug.description}
${bug.bugType ? `Bug Type: ${bug.bugType}\n` : ''}${bug.preconditions ? `Preconditions: ${bug.preconditions}\n` : ''}${bug.stepsToReproduce ? `Steps to Reproduce: ${bug.stepsToReproduce}\n` : ''}${bug.actualResult ? `Actual Result: ${bug.actualResult}\n` : ''}${bug.expectedResult ? `Expected Result: ${bug.expectedResult}\n` : ''}${bug.environment ? `Environment: Browser: ${bug.environment.browser || 'N/A'}, OS: ${bug.environment.os || 'N/A'}\n` : ''}Created: ${dateCreated}
                    </div>
                </div>
            </div>
        `;


        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('bugDetailsModal'));
        modal.show();

        // Re-initialize copy buttons for the modal content
        setTimeout(() => {
            initializeCopyButtons();
        }, 100);
    }
}

// Create a table row for a bug - removed duplicate function

// Load bugs from localStorage
function loadBugs() {
    const bugs = getBugsFromStorage();

    // Clear the current list
    bugList.innerHTML = '';

    if (bugs.length === 0) {
        noBugsMessage.classList.remove('d-none');
    } else {
        noBugsMessage.classList.add('d-none');

        // Sort bugs by date (newest first)
        bugs.sort((a, b) => new Date(b.dateCreated) - new Date(a.dateCreated));

        // Add each bug to the list
        bugs.forEach(bug => {
            const row = createBugRow(bug);
            bugList.appendChild(row);
        });
    }
    // Re-initialize copy buttons for any dynamically added content
    setTimeout(() => {
        initializeCopyButtons();
    }, 100);
}

// Get bugs from localStorage
function getBugsFromStorage() {
    const bugsJson = localStorage.getItem('bugs');
    return bugsJson ? JSON.parse(bugsJson) : [];
}

// Save bugs to localStorage
function saveBugsToStorage(bugs) {
    localStorage.setItem('bugs', JSON.stringify(bugs));
}

// Load settings from localStorage
function loadSettings() {
    // Default settings if not set
    if (localStorage.getItem('useAiGeneration') === null) {
        localStorage.setItem('useAiGeneration', 'true');
    }

    // Set default API key if not already set
    if (!localStorage.getItem('apiKey')) {
        localStorage.setItem('apiKey', 'AIzaSyBSBV--lZOn2UnHlGdzT-nOlcX9d73p9eY');
    }
}

// Generate bug details with Multi-API system or fallback to Google Gemini API
async function generateBugDetailsWithAI(title, apiKey) {
    // Preconditions and input validation
    if (!title || typeof title !== 'string' || title.trim() === '') {
        console.error('Invalid bug title provided:', title);
        throw new Error('Invalid bug title provided: Title must be a non-empty string');
    }

    // Check if multi-API is enabled
    const useMultiAPI = localStorage.getItem('useMultiAPI') === 'true';

    if (useMultiAPI) {
        try {
            console.log('🚀 Using Multi-API system for bug details generation');

            // Create comprehensive prompt for bug details generation
            const prompt = `
Generate comprehensive bug details based on this title:

**Bug Title:** ${title}

Provide detailed bug information in the following JSON format:
{
    "description": "Detailed description of the bug based on the title",
    "severity": "Critical|High|Medium|Low",
    "steps": "Step-by-step reproduction instructions",
    "actualResult": "What actually happens when the bug occurs",
    "expectedResult": "What should happen instead",
    "category": "UI/UX|Performance|Security|Functional|Integration|Mobile",
    "impact": "Description of user and business impact",
    "workaround": "Temporary workaround if available",
    "additionalInfo": "Any additional relevant information"
}

Guidelines:
1. Infer the most likely scenario from the bug title
2. Create realistic and specific reproduction steps
3. Predict appropriate severity based on the issue type
4. Provide clear expected vs actual results
5. Consider user experience and business impact
6. Make the description detailed but concise
7. Ensure all fields are filled with relevant information

Focus on creating a complete, professional bug report that a developer could work with.
`;

            const apiResponse = await multiAPIManager.makeAPIRequest(prompt, 'bug-analysis', {
                requestId: 'generateBtn'
            });

            // Parse the AI response
            let bugDetails;
            try {
                const jsonMatch = apiResponse.result.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    bugDetails = JSON.parse(jsonMatch[0]);

                    // Ensure all required fields are present
                    const result = {
                        description: bugDetails.description || `Detailed analysis of: ${title}`,
                        severity: bugDetails.severity || 'Medium',
                        steps: bugDetails.steps || 'Steps to reproduce this issue need to be determined.',
                        actualResult: bugDetails.actualResult || 'The system exhibits unexpected behavior.',
                        expectedResult: bugDetails.expectedResult || 'The system should work as intended.',
                        category: bugDetails.category || 'Functional',
                        impact: bugDetails.impact || 'Impact assessment needed',
                        workaround: bugDetails.workaround || 'No workaround available',
                        additionalInfo: bugDetails.additionalInfo || '',
                        aiGenerated: true,
                        apiUsed: apiResponse.apiUsed,
                        responseTime: apiResponse.responseTime,
                        multiAPIMode: true,
                        fromCache: apiResponse.fromCache || false
                    };

                    const cacheInfo = apiResponse.fromCache ? ' (cached)' : '';
                    console.log(`✅ Bug details generated using ${multiAPIManager.apiConfigs[apiResponse.apiUsed].name}${cacheInfo}`);
                    return result;
                } else {
                    throw new Error('No JSON found in response');
                }
            } catch (parseError) {
                console.warn('Failed to parse AI response as JSON, using fallback parsing');
                bugDetails = parseAIResponseToBugDetails(apiResponse.result, title);
                bugDetails.apiUsed = apiResponse.apiUsed;
                bugDetails.fromCache = apiResponse.fromCache || false;
                return bugDetails;
            }
        } catch (error) {
            console.warn('Multi-API bug details generation failed, falling back to local AI:', error);
        }
    }

    // API key validation for fallback
    if (!apiKey || typeof apiKey !== 'string') {
        console.warn('No API key provided, using default key');
        apiKey = 'AIzaSyBSBV--lZOn2UnHlGdzT-nOlcX9d73p9eY';
    }

    // Try to use custom local AI first with enhanced title analysis
    try {
        console.log(`Attempting to generate bug details using enhanced custom AI for: "${title}"...`);
        const customAIResult = await generateWithCustomAI(title);
        console.log('Successfully generated bug details with custom AI');
        return customAIResult;
    } catch (customAIError) {
        console.warn('Custom AI generation failed, falling back to Google Gemini API:', customAIError.message);
        // Continue with Google Gemini API as fallback
    }

    // Validate API key format using regex pattern
    const apiKeyRegex = /^AIza[0-9A-Za-z\-_]{35}$/;
    if (!apiKeyRegex.test(apiKey)) {
        console.warn('API key format is invalid. Using default key instead.');
        apiKey = 'AIzaSyBSBV--lZOn2UnHlGdzT-nOlcX9d73p9eY';
    }

    const endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

    // Enhanced prompt for more intelligent title-based analysis
    const prompt = `Generate detailed and intelligent information for a software bug with the title: "${title}"

Analyze the title deeply to extract as much context as possible. Consider:
- The type of bug (UI, functional, performance, security, etc.)
- The potential severity based on impact
- The likely affected components or systems
- Possible root causes
- Technical implications

Please provide the following in JSON format:
1. A comprehensive description of the bug that shows deep understanding of the issue
2. The severity level (Low, Medium, High, or Critical) with justification
3. Detailed steps to reproduce the bug that are specific to this type of issue
4. The actual result observed with technical details
5. The expected result with clear acceptance criteria

Format your response as a valid JSON object with these keys: description, severity, steps, actualResult, expectedResult.`;

    try {
        console.log(`Sending request to Gemini API for bug title: "${title}"...`);

        // Prepare request with validated parameters
        const requestUrl = `${endpoint}?key=${apiKey}`;
        const requestBody = {
            contents: [
                {
                    parts: [
                        {
                            text: prompt
                        }
                    ]
                }
            ],
            generationConfig: {
                temperature: 0.7
            }
        };

        console.log('Request body structure:', JSON.stringify(requestBody, null, 2).substring(0, 200) + '...');

        const response = await fetch(requestUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        // Enhanced error handling for HTTP errors
        if (!response.ok) {
            let errorText = '';
            try {
                errorText = await response.text();
            } catch (e) {
                errorText = 'Could not extract error details';
            }

            console.error(`Gemini API error (${response.status}):`, errorText);
            throw new Error(`API request failed with status ${response.status}: ${errorText}`);
        }

        // Parse response with error handling
        let data;
        try {
            data = await response.json();
            console.log('Gemini API response received successfully');
        } catch (jsonError) {
            console.error('Failed to parse API response as JSON:', jsonError);
            throw new Error('Invalid JSON response from API');
        }

        // Validate response structure with detailed error messages
        if (!data) {
            throw new Error('Empty response received from API');
        }

        if (!data.candidates) {
            console.error('Missing candidates in API response:', data);
            throw new Error('API response missing candidates array');
        }

        if (!data.candidates[0]) {
            console.error('Empty candidates array in API response:', data);
            throw new Error('API response contains empty candidates array');
        }

        if (!data.candidates[0].content) {
            console.error('Missing content in API response candidate:', data.candidates[0]);
            throw new Error('API response candidate missing content');
        }

        if (!data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
            console.error('Missing parts in API response content:', data.candidates[0].content);
            throw new Error('API response content missing parts');
        }

        const content = data.candidates[0].content.parts[0].text;
        if (!content) {
            console.error('Empty text in API response part:', data.candidates[0].content.parts[0]);
            throw new Error('API response part has empty text');
        }

        // Extract JSON from the response with improved error handling
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
            console.error('No JSON object found in response text:', content.substring(0, 100) + '...');
            throw new Error('No JSON found in API response');
        }

        // Parse JSON with validation
        let parsedData;
        try {
            parsedData = JSON.parse(jsonMatch[0]);
            console.log('Successfully parsed JSON from API response');
        } catch (parseError) {
            console.error('Error parsing JSON from API response:', parseError, '\nJSON string:', jsonMatch[0].substring(0, 100) + '...');
            throw new Error('Failed to parse JSON from API response: ' + parseError.message);
        }

        // Validate parsed data and apply defaults with detailed logging
        const allowedSeverities = ['Low', 'Medium', 'High', 'Critical'];
        const result = {
            description: parsedData.description || `Issue with ${title}`,
            severity: parsedData.severity || 'Medium',
            steps: parsedData.steps || `1. Navigate to the affected area\n2. Attempt to reproduce the "${title}" issue`,
            actualResult: parsedData.actualResult || `When performing the action related to "${title}", the system behaves unexpectedly.`,
            expectedResult: parsedData.expectedResult || `The system should handle the "${title}" action correctly.`
        };

        // Log missing fields
        if (!parsedData.description) console.warn('API response missing description field, using default');
        if (!parsedData.severity) console.warn('API response missing severity field, using default');
        if (!parsedData.steps) console.warn('API response missing steps field, using default');
        if (!parsedData.actualResult) console.warn('API response missing actualResult field, using default');
        if (!parsedData.expectedResult) console.warn('API response missing expectedResult field, using default');

        // Validate severity is one of the allowed values
        if (!allowedSeverities.includes(result.severity)) {
            console.warn(`Invalid severity value: "${result.severity}", defaulting to 'Medium'`);
            result.severity = 'Medium';
        }

        console.log('Successfully generated bug details with Google Gemini API');
        return result;
    } catch (error) {
        console.error('Error in Google Gemini API generation:', error);

        // If both custom AI and Google API fail, use the local generation as final fallback
        console.warn('Falling back to local generation as both custom AI and Google API failed');
        return generateBugDetailsFromTitle(title);
    }
}

// Custom AI implementation for generating bug details
async function generateWithCustomAI(title) {
    return new Promise((resolve, reject) => {
        // Simulate API processing time
        setTimeout(() => {
            try {
                console.log('Processing with custom AI...');

                // Advanced bug patterns with more detailed templates
                const bugPatterns = [
                    {
                        keywords: ['crash', 'freeze', 'hang', 'not responding'],
                        severity: 'Critical',
                        template: 'Application crash or freeze',
                        description: `The application crashes or freezes when attempting to ${title}. This is a critical issue that prevents users from completing their tasks and may result in data loss.`,
                        steps: `1. Open the application\n2. Navigate to the section related to "${title}"\n3. Attempt to perform the action\n4. Observe the application freezing or crashing\n5. Check system logs for error messages`,
                        actualResult: `The application becomes unresponsive or terminates unexpectedly when attempting to ${title}.`,
                        expectedResult: `The application should remain stable and responsive while performing the "${title}" operation.`
                    },
                    {
                        keywords: ['slow', 'performance', 'lag', 'delay', 'timeout'],
                        severity: 'High',
                        template: 'Performance degradation',
                        description: `Significant performance degradation observed during ${title}. This impacts user experience and productivity.`,
                        steps: `1. Open the application\n2. Navigate to the feature related to "${title}"\n3. Perform the action multiple times\n4. Measure response time\n5. Compare with expected performance benchmarks`,
                        actualResult: `The "${title}" operation takes an excessive amount of time to complete, causing user frustration.`,
                        expectedResult: `The "${title}" operation should complete within acceptable performance parameters (typically under 2 seconds).`
                    },
                    {
                        keywords: ['button', 'click', 'ui', 'interface', 'menu', 'navigation'],
                        severity: 'Medium',
                        template: 'User interface issue',
                        description: `User interface problem detected with ${title}. Elements are not functioning as expected or are displaying incorrectly.`,
                        steps: `1. Navigate to the screen containing the "${title}" element\n2. Observe the UI element's appearance\n3. Attempt to interact with the element\n4. Note any visual or functional anomalies\n5. Try different browsers/devices if applicable`,
                        actualResult: `The UI element related to "${title}" does not respond correctly to user interaction or displays improperly.`,
                        expectedResult: `The UI element should be properly rendered and respond appropriately to user interactions.`
                    },
                    {
                        keywords: ['typo', 'spelling', 'text', 'label', 'message'],
                        severity: 'Low',
                        template: 'Text or content issue',
                        description: `Text-related issue identified in ${title}. This includes spelling errors, grammatical mistakes, or incorrect content.`,
                        steps: `1. Navigate to the screen containing the "${title}" text\n2. Locate the specific text element\n3. Verify the spelling, grammar, and accuracy of the content\n4. Document the exact error and its location`,
                        actualResult: `The text related to "${title}" contains errors or is displayed incorrectly.`,
                        expectedResult: `All text should be grammatically correct, properly spelled, and accurately convey the intended information.`
                    },
                    {
                        keywords: ['login', 'auth', 'password', 'credential', 'permission', 'access'],
                        severity: 'High',
                        template: 'Authentication or authorization issue',
                        description: `Authentication problem encountered with ${title}. Users may be unable to access their accounts or certain features.`,
                        steps: `1. Attempt to access the system using valid credentials\n2. Navigate to the "${title}" feature\n3. Observe authentication behavior\n4. Try different user accounts with varying permission levels\n5. Check for error messages during the authentication process`,
                        actualResult: `Users experience authentication failures or improper authorization when attempting to ${title}.`,
                        expectedResult: `The system should correctly authenticate users and grant appropriate access based on their permissions.`
                    },
                    {
                        keywords: ['data', 'save', 'load', 'database', 'storage', 'file'],
                        severity: 'High',
                        template: 'Data handling issue',
                        description: `Data integrity or persistence issue related to ${title}. Information may be lost, corrupted, or improperly stored.`,
                        steps: `1. Create or modify data related to "${title}"\n2. Save or submit the data\n3. Verify the data was properly stored\n4. Attempt to retrieve or view the data\n5. Check for any discrepancies between saved and retrieved data`,
                        actualResult: `Data related to "${title}" is not being properly saved, retrieved, or maintained.`,
                        expectedResult: `All data should be accurately stored, retrieved, and maintained throughout the system.`
                    },
                    {
                        keywords: ['display', 'render', 'show', 'view', 'layout', 'visual'],
                        severity: 'Medium',
                        template: 'Display or rendering issue',
                        description: `Visual rendering problem detected with ${title}. Content may be displayed incorrectly or incompletely.`,
                        steps: `1. Navigate to the screen containing the "${title}" element\n2. Observe how the content is rendered\n3. Resize the window or rotate the device if applicable\n4. Test on different browsers, devices, or screen resolutions\n5. Note any visual inconsistencies or rendering artifacts`,
                        actualResult: `The "${title}" element or content is not displayed correctly across all required environments.`,
                        expectedResult: `All visual elements should render consistently and correctly across supported browsers, devices, and screen sizes.`
                    },
                    {
                        keywords: ['error', 'exception', 'fail', 'invalid', 'incorrect'],
                        severity: 'High',
                        template: 'Error handling issue',
                        description: `Improper error handling detected in ${title}. The system may not gracefully handle exceptional conditions.`,
                        steps: `1. Navigate to the feature related to "${title}"\n2. Deliberately trigger error conditions (e.g., invalid input, network disconnection)\n3. Observe how the system responds\n4. Check for appropriate error messages\n5. Verify that the system recovers gracefully`,
                        actualResult: `When errors occur during "${title}", the system fails to handle them properly or provide useful feedback.`,
                        expectedResult: `The system should gracefully handle errors, provide clear error messages, and maintain data integrity.`
                    },
                    {
                        keywords: ['api', 'integration', 'service', 'endpoint', 'request', 'response'],
                        severity: 'High',
                        template: 'API or integration issue',
                        description: `Integration problem identified with ${title}. Communication with external systems or APIs may be failing.`,
                        steps: `1. Initiate the operation that triggers the "${title}" API call\n2. Monitor network traffic and API responses\n3. Verify request and response formats\n4. Check for timeout or latency issues\n5. Validate error handling for API failures`,
                        actualResult: `The integration with external systems for "${title}" is failing or returning unexpected results.`,
                        expectedResult: `All API calls and integrations should function correctly, with proper error handling for edge cases.`
                    },
                    {
                        keywords: ['security', 'vulnerability', 'exploit', 'injection', 'xss', 'csrf'],
                        severity: 'Critical',
                        template: 'Security vulnerability',
                        description: `Potential security vulnerability detected in ${title}. This could expose sensitive data or allow unauthorized access.`,
                        steps: `1. Navigate to the feature related to "${title}"\n2. Attempt to exploit the suspected vulnerability\n3. Document the specific security concern\n4. Assess the potential impact and attack vectors\n5. Verify if sensitive data is properly protected`,
                        actualResult: `The "${title}" feature contains a security vulnerability that could be exploited.`,
                        expectedResult: `All features should be secure against common vulnerabilities and protect sensitive data.`
                    }
                ];

                // Default values
                let result = {
                    description: `Issue with ${title}. This requires investigation to determine the root cause and impact.`,
                    severity: 'Medium',
                    steps: `1. Navigate to the affected area\n2. Attempt to reproduce the "${title}" issue\n3. Observe the unexpected behavior\n4. Try alternative approaches to verify consistency`,
                    actualResult: `When performing the action related to "${title}", the system behaves unexpectedly.`,
                    expectedResult: `The system should handle the "${title}" action correctly and provide appropriate feedback.`
                };

                // Find matching pattern with more sophisticated matching
                const titleLower = title.toLowerCase();
                let bestMatch = null;
                let highestMatchScore = 0;

                for (const pattern of bugPatterns) {
                    let matchScore = 0;
                    for (const keyword of pattern.keywords) {
                        if (titleLower.includes(keyword)) {
                            // Increase score based on keyword length (longer keywords are more specific)
                            matchScore += keyword.length;
                        }
                    }

                    if (matchScore > highestMatchScore) {
                        highestMatchScore = matchScore;
                        bestMatch = pattern;
                    }
                }

                // Apply the best matching pattern if found
                if (bestMatch && highestMatchScore > 0) {
                    result = {
                        description: bestMatch.description,
                        severity: bestMatch.severity,
                        steps: bestMatch.steps,
                        actualResult: bestMatch.actualResult,
                        expectedResult: bestMatch.expectedResult
                    };
                } else {
                    // If no pattern matches, use NLP-like techniques to generate more contextual content
                    // Extract key nouns and verbs from the title
                    const words = titleLower.split(/\s+/);
                    const action = words.find(word => ['not', 'fails', 'broken', 'missing', 'incorrect'].includes(word)) || 'fails';

                    // Generate more contextual description
                    result.description = `Issue detected: ${title}. The functionality appears to be ${action}, which may impact user experience.`;
                    result.steps = `1. Navigate to the section containing the "${title}" functionality\n2. Attempt to use the feature as intended\n3. Observe the unexpected behavior where it ${action}\n4. Try alternative approaches to verify the issue is consistent`;
                    result.actualResult = `When attempting to use the "${title}" feature, it ${action} to perform as expected.`;
                    result.expectedResult = `The "${title}" feature should work correctly according to specifications and user expectations.`;
                }

                // Add some randomness to make it feel more AI-like
                const randomAdjustment = Math.random() < 0.2;

                if (randomAdjustment) {
                    // Occasionally adjust severity based on certain keywords
                    if (titleLower.includes('critical') || titleLower.includes('urgent')) {
                        result.severity = 'Critical';
                    } else if (titleLower.includes('minor') || titleLower.includes('cosmetic')) {
                        result.severity = 'Low';
                    }
                }

                console.log('Custom AI generated result:', result);
                resolve(result);
            } catch (error) {
                console.error('Error in custom AI generation:', error);
                reject(new Error('Custom AI processing failed: ' + error.message));
            }
        }, 500); // Simulate processing time
    });
}

// ===== SMART AI FEATURES =====

// Initialize Smart AI Features
function initializeSmartFeatures() {
    // Add event listeners for real-time analysis
    titleInput.addEventListener('input', debounce(analyzeTitle, 500));
    descriptionInput.addEventListener('input', debounce(analyzeDescription, 500));
    severityInput.addEventListener('change', validateSeverity);
    stepsInput.addEventListener('input', debounce(updateQualityScore, 300));
    actualResultInput.addEventListener('input', debounce(updateQualityScore, 300));
    expectedResultInput.addEventListener('input', debounce(updateQualityScore, 300));

    // Initial quality score update
    updateQualityScore();
}

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Analyze title for suggestions and duplicates
function analyzeTitle() {
    const title = titleInput.value.trim();

    if (title.length < 3) {
        titleSuggestions.innerHTML = '';
        duplicateWarning.innerHTML = '';
        return;
    }

    // Check for duplicates
    checkForDuplicates(title);

    // Provide title improvement suggestions
    provideTitleSuggestions(title);

    // Auto-detect bug category
    detectBugCategory(title);

    // Update quality score
    updateQualityScore();
}

// Check for potential duplicate bugs
function checkForDuplicates(title) {
    const bugs = getBugsFromStorage();
    const currentBugId = document.getElementById('bugId').value;

    const similarBugs = bugs.filter(bug => {
        if (bug.id === currentBugId) return false;
        return calculateSimilarity(title.toLowerCase(), bug.title.toLowerCase()) > 0.6;
    });

    if (similarBugs.length > 0) {
        const warningHtml = `
            <div class="alert alert-warning alert-sm py-2 px-3 mb-0">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>Potential duplicate:</strong> Similar to "${similarBugs[0].title}"
                <button class="btn btn-sm btn-outline-warning ms-2" onclick="viewBugDetails('${similarBugs[0].id}')">
                    View Similar
                </button>
            </div>
        `;
        duplicateWarning.innerHTML = warningHtml;
    } else {
        duplicateWarning.innerHTML = '';
    }
}

// Calculate text similarity using Levenshtein distance
function calculateSimilarity(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    for (let i = 0; i <= len2; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= len1; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= len2; i++) {
        for (let j = 1; j <= len1; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    const maxLen = Math.max(len1, len2);
    return maxLen === 0 ? 1 : (maxLen - matrix[len2][len1]) / maxLen;
}

// Provide title improvement suggestions
function provideTitleSuggestions(title) {
    const suggestions = [];

    // Check if title is too short
    if (title.length < 10) {
        suggestions.push('Consider making the title more descriptive (at least 10 characters)');
    }

    // Check if title is too long
    if (title.length > 100) {
        suggestions.push('Title is quite long. Consider shortening it for better readability');
    }

    // Check for missing context
    const hasContext = /\b(when|while|after|during|on|in)\b/i.test(title);
    if (!hasContext && title.length > 15) {
        suggestions.push('Consider adding context (when/where the issue occurs)');
    }

    // Check for action words
    const hasAction = /\b(cannot|can\'t|unable|fails|error|crash|freeze|slow)\b/i.test(title);
    if (!hasAction) {
        suggestions.push('Consider adding what specifically goes wrong (fails, crashes, etc.)');
    }

    if (suggestions.length > 0) {
        const suggestionHtml = `
            <div class="alert alert-info alert-sm py-2 px-3 mb-0">
                <i class="bi bi-lightbulb"></i>
                <strong>Title suggestions:</strong>
                <ul class="mb-0 mt-1">
                    ${suggestions.map(s => `<li class="small">${s}</li>`).join('')}
                </ul>
            </div>
        `;
        titleSuggestions.innerHTML = suggestionHtml;
    } else {
        titleSuggestions.innerHTML = '';
    }
}

// Detect and display bug category
function detectBugCategory(title) {
    const categories = {
        'UI/UX': ['button', 'layout', 'design', 'interface', 'display', 'visual', 'alignment', 'color', 'font'],
        'Performance': ['slow', 'lag', 'freeze', 'timeout', 'loading', 'speed', 'memory', 'cpu'],
        'Security': ['login', 'password', 'auth', 'permission', 'access', 'security', 'vulnerability'],
        'Functional': ['feature', 'function', 'work', 'broken', 'not working', 'fails', 'error'],
        'Integration': ['api', 'service', 'connection', 'sync', 'import', 'export', 'third-party'],
        'Data': ['database', 'data', 'save', 'load', 'corrupt', 'missing', 'duplicate'],
        'Mobile': ['mobile', 'phone', 'tablet', 'touch', 'swipe', 'responsive'],
        'Browser': ['chrome', 'firefox', 'safari', 'edge', 'browser', 'compatibility']
    };

    const detectedCategories = [];
    const titleLower = title.toLowerCase();

    for (const [category, keywords] of Object.entries(categories)) {
        const matches = keywords.filter(keyword => titleLower.includes(keyword));
        if (matches.length > 0) {
            detectedCategories.push({ category, confidence: matches.length });
        }
    }

    if (detectedCategories.length > 0) {
        // Sort by confidence and take top 2
        detectedCategories.sort((a, b) => b.confidence - a.confidence);
        const topCategories = detectedCategories.slice(0, 2);

        const categoryHtml = topCategories.map(cat =>
            `<span class="badge bg-secondary">${cat.category}</span>`
        ).join(' ');

        bugCategory.innerHTML = categoryHtml;
        categorySection.style.display = 'block';
    } else {
        categorySection.style.display = 'none';
    }
}

// Analyze description for suggestions
function analyzeDescription() {
    const description = descriptionInput.value.trim();

    if (description.length < 3) {
        descriptionSuggestions.innerHTML = '';
        updateQualityScore();
        return;
    }

    const suggestions = [];

    // Check description length
    if (description.length < 20) {
        suggestions.push('Description is quite short. Add more details about the issue.');
    }

    // Check for technical details
    const hasTechnicalInfo = /\b(error|exception|code|status|version|browser|os)\b/i.test(description);
    if (!hasTechnicalInfo && description.length > 30) {
        suggestions.push('Consider adding technical details (error messages, browser, OS version).');
    }

    // Check for impact description
    const hasImpact = /\b(user|customer|business|affect|impact|prevent|block)\b/i.test(description);
    if (!hasImpact) {
        suggestions.push('Consider describing the impact on users or business.');
    }

    if (suggestions.length > 0) {
        const suggestionHtml = `
            <div class="alert alert-info alert-sm py-2 px-3 mb-0">
                <i class="bi bi-lightbulb"></i>
                <strong>Description suggestions:</strong>
                <ul class="mb-0 mt-1">
                    ${suggestions.map(s => `<li class="small">${s}</li>`).join('')}
                </ul>
            </div>
        `;
        descriptionSuggestions.innerHTML = suggestionHtml;
    } else {
        descriptionSuggestions.innerHTML = '';
    }

    // Suggest severity based on description
    suggestSeverity(description);

    updateQualityScore();
}

// Suggest severity based on content analysis
function suggestSeverity(description) {
    const title = titleInput.value.toLowerCase();
    const desc = description.toLowerCase();
    const combined = title + ' ' + desc;

    let suggestedSeverity = '';
    let confidence = 0;

    // Critical indicators
    const criticalKeywords = ['crash', 'data loss', 'security', 'cannot login', 'system down', 'critical error', 'complete failure'];
    const criticalMatches = criticalKeywords.filter(keyword => combined.includes(keyword)).length;

    // High indicators
    const highKeywords = ['error', 'broken', 'not working', 'fails', 'major', 'important feature'];
    const highMatches = highKeywords.filter(keyword => combined.includes(keyword)).length;

    // Medium indicators
    const mediumKeywords = ['slow', 'minor', 'sometimes', 'occasionally', 'improvement'];
    const mediumMatches = mediumKeywords.filter(keyword => combined.includes(keyword)).length;

    // Low indicators
    const lowKeywords = ['cosmetic', 'typo', 'suggestion', 'enhancement', 'nice to have'];
    const lowMatches = lowKeywords.filter(keyword => combined.includes(keyword)).length;

    if (criticalMatches > 0) {
        suggestedSeverity = 'Critical';
        confidence = Math.min(criticalMatches * 30, 90);
    } else if (highMatches > 0) {
        suggestedSeverity = 'High';
        confidence = Math.min(highMatches * 25, 80);
    } else if (lowMatches > 0) {
        suggestedSeverity = 'Low';
        confidence = Math.min(lowMatches * 20, 70);
    } else if (mediumMatches > 0) {
        suggestedSeverity = 'Medium';
        confidence = Math.min(mediumMatches * 20, 70);
    } else {
        suggestedSeverity = 'Medium';
        confidence = 50;
    }

    if (suggestedSeverity && confidence > 40) {
        const currentSeverity = severityInput.value;
        if (!currentSeverity || currentSeverity !== suggestedSeverity) {
            const suggestionHtml = `
                <div class="alert alert-success alert-sm py-2 px-3 mb-0">
                    <i class="bi bi-robot"></i>
                    <strong>AI suggests:</strong> ${suggestedSeverity} severity (${confidence}% confidence)
                    <button class="btn btn-sm btn-outline-success ms-2" onclick="applySeveritySuggestion('${suggestedSeverity}')">
                        Apply
                    </button>
                </div>
            `;
            severitySuggestion.innerHTML = suggestionHtml;
        } else {
            severitySuggestion.innerHTML = '';
        }
    } else {
        severitySuggestion.innerHTML = '';
    }
}

// Apply suggested severity
function applySeveritySuggestion(severity) {
    severityInput.value = severity;
    severitySuggestion.innerHTML = '';
    updateQualityScore();
    showToast(`Severity set to ${severity}`, 'success');
}

// Validate severity selection
function validateSeverity() {
    const severity = severityInput.value;
    const title = titleInput.value.toLowerCase();
    const description = descriptionInput.value.toLowerCase();

    if (!severity) {
        updateQualityScore();
        return;
    }

    // Check for severity mismatch
    const combined = title + ' ' + description;
    let warning = '';

    if (severity === 'Critical') {
        const hasCriticalIndicators = /\b(crash|data loss|security|cannot login|system down)\b/i.test(combined);
        if (!hasCriticalIndicators && combined.length > 20) {
            warning = 'Critical severity selected but description doesn\'t indicate critical impact. Consider if this is truly critical.';
        }
    } else if (severity === 'Low') {
        const hasHighImpactIndicators = /\b(error|broken|not working|fails|crash)\b/i.test(combined);
        if (hasHighImpactIndicators) {
            warning = 'Low severity selected but description indicates significant issues. Consider higher severity.';
        }
    }

    if (warning) {
        const warningHtml = `
            <div class="alert alert-warning alert-sm py-2 px-3 mb-0">
                <i class="bi bi-exclamation-triangle"></i>
                <strong>Severity check:</strong> ${warning}
            </div>
        `;
        severitySuggestion.innerHTML = warningHtml;
    }

    updateQualityScore();
}

// Update bug report quality score
function updateQualityScore() {
    const title = titleInput.value.trim();
    const description = descriptionInput.value.trim();
    const severity = severityInput.value;
    const steps = stepsInput.value.trim();
    const actualResult = actualResultInput.value.trim();
    const expectedResult = expectedResultInput.value.trim();

    let score = 0;
    const feedback = [];
    const missing = [];

    // Title scoring (20 points)
    if (title.length >= 10 && title.length <= 100) {
        score += 20;
    } else if (title.length > 0) {
        score += 10;
        if (title.length < 10) feedback.push('Title could be more descriptive');
        if (title.length > 100) feedback.push('Title is quite long');
    } else {
        missing.push('Title is required');
    }

    // Description scoring (25 points)
    if (description.length >= 50) {
        score += 25;
    } else if (description.length >= 20) {
        score += 15;
        feedback.push('Description could be more detailed');
    } else if (description.length > 0) {
        score += 8;
        feedback.push('Description is too brief');
    } else {
        missing.push('Description helps explain the issue');
    }

    // Severity scoring (15 points)
    if (severity) {
        score += 15;
    } else {
        missing.push('Severity level');
    }

    // Steps to reproduce scoring (20 points)
    if (steps.length >= 30) {
        score += 20;
    } else if (steps.length >= 10) {
        score += 12;
        feedback.push('Steps could be more detailed');
    } else if (steps.length > 0) {
        score += 6;
        feedback.push('Steps are too brief');
    } else {
        missing.push('Steps to reproduce');
    }

    // Actual result scoring (10 points)
    if (actualResult.length >= 10) {
        score += 10;
    } else if (actualResult.length > 0) {
        score += 5;
        feedback.push('Actual result could be more specific');
    } else {
        missing.push('Actual result description');
    }

    // Expected result scoring (10 points)
    if (expectedResult.length >= 10) {
        score += 10;
    } else if (expectedResult.length > 0) {
        score += 5;
        feedback.push('Expected result could be more specific');
    } else {
        missing.push('Expected result description');
    }

    // Show quality score if any field has content
    const hasContent = title || description || severity || steps || actualResult || expectedResult;

    if (hasContent) {
        qualityScoreCard.style.display = 'block';

        // Update score display
        qualityScore.textContent = `${score}%`;
        qualityProgress.style.width = `${score}%`;

        // Update progress bar color based on score
        qualityProgress.className = 'progress-bar';
        if (score >= 80) {
            qualityProgress.classList.add('bg-success');
        } else if (score >= 60) {
            qualityProgress.classList.add('bg-warning');
        } else {
            qualityProgress.classList.add('bg-danger');
        }

        // Update feedback
        let feedbackText = '';
        if (score >= 90) {
            feedbackText = '🎉 Excellent bug report! Very detailed and complete.';
        } else if (score >= 80) {
            feedbackText = '👍 Good bug report! Minor improvements possible.';
        } else if (score >= 60) {
            feedbackText = '👌 Decent bug report. Some important details missing.';
        } else if (score >= 40) {
            feedbackText = '⚠️ Basic bug report. Needs more information.';
        } else {
            feedbackText = '❌ Incomplete bug report. Please add more details.';
        }

        qualityFeedback.textContent = feedbackText;

        // Show missing suggestions
        if (missing.length > 0) {
            const missingHtml = `
                <div class="alert alert-warning alert-sm py-2 px-3 mb-0 mt-2">
                    <strong>Missing:</strong> ${missing.join(', ')}
                </div>
            `;
            missingSuggestions.innerHTML = missingHtml;
        } else if (feedback.length > 0) {
            const feedbackHtml = `
                <div class="alert alert-info alert-sm py-2 px-3 mb-0 mt-2">
                    <strong>Suggestions:</strong> ${feedback.join(', ')}
                </div>
            `;
            missingSuggestions.innerHTML = feedbackHtml;
        } else {
            missingSuggestions.innerHTML = '';
        }
    } else {
        qualityScoreCard.style.display = 'none';
    }
}


// Track initialization state
let advancedFeaturesInitialized = false;

// Initialize all advanced features
function initializeAdvancedFeatures() {
    // Prevent multiple initializations
    if (advancedFeaturesInitialized) {
        return;
    }

    advancedFeaturesInitialized = true;

    // Template system
    initializeTemplates();

    // Voice input and screenshot tools removed

    // Analytics
    initializeAnalytics();

    // Export functionality
    initializeExports();

    // Search and filtering
    initializeSearchAndFilter();

    // File upload and evidence
    initializeFileUpload();
}

// ===== TEMPLATE SYSTEM =====

function initializeTemplates() {
    // Template dropdown handlers - prevent duplicate listeners
    document.querySelectorAll('[data-template]:not([data-template-initialized])').forEach(item => {
        item.setAttribute('data-template-initialized', 'true');
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const template = e.currentTarget.dataset.template;
            applyTemplate(template);
        });
    });

    // Template wizard button - prevent duplicate listeners
    const wizardBtn = document.getElementById('templateWizardBtn');
    if (wizardBtn && !wizardBtn.hasAttribute('data-wizard-initialized')) {
        wizardBtn.setAttribute('data-wizard-initialized', 'true');
        wizardBtn.addEventListener('click', () => {
            const modal = new bootstrap.Modal(document.getElementById('templateWizardModal'));
            modal.show();
        });
    }

    // Template cards in wizard - prevent duplicate listeners
    document.querySelectorAll('.template-card:not([data-card-initialized])').forEach(card => {
        card.setAttribute('data-card-initialized', 'true');
        card.addEventListener('click', () => {
            const template = card.dataset.template;
            applyTemplate(template);
            const modalInstance = bootstrap.Modal.getInstance(document.getElementById('templateWizardModal'));
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    });
}

// Debounce variable for template application
let templateApplyTimeout = null;

function applyTemplate(templateType) {
    // Prevent rapid successive calls
    if (templateApplyTimeout) {
        clearTimeout(templateApplyTimeout);
    }

    templateApplyTimeout = setTimeout(() => {
        applyTemplateImmediate(templateType);
        templateApplyTimeout = null;
    }, 100);
}

function applyTemplateImmediate(templateType) {
    // Handle custom template case
    if (templateType === 'custom') {
        showToast('Custom template feature has been removed for simplicity.', 'info');
        return;
    }

    const templates = {
        general: {
            title: '',
            description: 'Please describe the issue you encountered...',
            severity: 'Medium',
            steps: '1. Navigate to...\n2. Click on...\n3. Observe the issue',
            actualResult: 'What actually happened...',
            expectedResult: 'What should have happened...'
        },
        ui: {
            title: 'UI Issue: ',
            description: 'Visual or layout problem observed in the user interface...',
            severity: 'Medium',
            steps: '1. Open the application\n2. Navigate to the affected page\n3. Observe the visual issue',
            actualResult: 'The UI element appears incorrectly...',
            expectedResult: 'The UI element should display properly...'
        },
        performance: {
            title: 'Performance Issue: ',
            description: 'Application is running slower than expected...',
            severity: 'High',
            steps: '1. Perform the action that is slow\n2. Measure the time taken\n3. Compare with expected performance',
            actualResult: 'The operation takes X seconds...',
            expectedResult: 'The operation should complete in Y seconds...'
        },
        security: {
            title: 'Security Issue: ',
            description: 'Potential security vulnerability or access control problem...',
            severity: 'Critical',
            steps: '1. Attempt to access restricted area\n2. Observe security behavior\n3. Document the security gap',
            actualResult: 'Unauthorized access was possible...',
            expectedResult: 'Access should be denied for unauthorized users...'
        },
        mobile: {
            title: 'Mobile Issue: ',
            description: 'Problem specific to mobile devices or responsive design...',
            severity: 'Medium',
            steps: '1. Open on mobile device\n2. Navigate to affected area\n3. Observe mobile-specific issue',
            actualResult: 'On mobile, the feature behaves incorrectly...',
            expectedResult: 'The feature should work properly on mobile...'
        },
        integration: {
            title: 'Integration Issue: ',
            description: 'Problem with external service integration or API calls...',
            severity: 'High',
            steps: '1. Trigger the integration\n2. Monitor API calls\n3. Observe the failure',
            actualResult: 'The integration fails with error...',
            expectedResult: 'The integration should work seamlessly...'
        }
    };

    const template = templates[templateType];
    if (template) {
        if (template.title) titleInput.value = template.title;
        if (template.description) descriptionInput.value = template.description;
        if (template.severity) severityInput.value = template.severity;
        if (template.steps) stepsInput.value = template.steps;
        if (template.actualResult) actualResultInput.value = template.actualResult;
        if (template.expectedResult) expectedResultInput.value = template.expectedResult;

        // Update quality score
        updateQualityScore();

        showToast(`${templateType.toUpperCase()} template applied!`, 'success');
    }
}

// Voice input system removed

// ===== ENVIRONMENT DETECTION =====

function detectEnvironment() {
    const browserInfo = document.getElementById('browserInfo');
    const osInfo = document.getElementById('osInfo');
    const screenResolution = document.getElementById('screenResolution');
    const userAgent = document.getElementById('userAgent');

    if (browserInfo) {
        // Detect browser
        const browser = detectBrowser();
        browserInfo.value = browser;
    }

    if (osInfo) {
        // Detect OS
        const os = detectOS();
        osInfo.value = os;
    }

    if (screenResolution) {
        // Get screen resolution
        screenResolution.value = `${screen.width}x${screen.height}`;
    }

    if (userAgent) {
        // Simplified user agent
        userAgent.value = navigator.userAgent.substring(0, 50) + '...';
    }
}

function detectBrowser() {
    const userAgent = navigator.userAgent;

    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
        const version = userAgent.match(/Chrome\/(\d+)/);
        return `Chrome ${version ? version[1] : 'Unknown'}`;
    } else if (userAgent.includes('Firefox')) {
        const version = userAgent.match(/Firefox\/(\d+)/);
        return `Firefox ${version ? version[1] : 'Unknown'}`;
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
        const version = userAgent.match(/Version\/(\d+)/);
        return `Safari ${version ? version[1] : 'Unknown'}`;
    } else if (userAgent.includes('Edg')) {
        const version = userAgent.match(/Edg\/(\d+)/);
        return `Edge ${version ? version[1] : 'Unknown'}`;
    }

    return 'Unknown Browser';
}

function detectOS() {
    const userAgent = navigator.userAgent;

    if (userAgent.includes('Windows NT 10.0')) return 'Windows 10/11';
    if (userAgent.includes('Windows NT 6.3')) return 'Windows 8.1';
    if (userAgent.includes('Windows NT 6.2')) return 'Windows 8';
    if (userAgent.includes('Windows NT 6.1')) return 'Windows 7';
    if (userAgent.includes('Mac OS X')) {
        const version = userAgent.match(/Mac OS X ([\d_]+)/);
        return `macOS ${version ? version[1].replace(/_/g, '.') : 'Unknown'}`;
    }
    if (userAgent.includes('Linux')) return 'Linux';
    if (userAgent.includes('Android')) {
        const version = userAgent.match(/Android ([\d.]+)/);
        return `Android ${version ? version[1] : 'Unknown'}`;
    }
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
        const version = userAgent.match(/OS ([\d_]+)/);
        return `iOS ${version ? version[1].replace(/_/g, '.') : 'Unknown'}`;
    }

    return 'Unknown OS';
}

// ===== FILE UPLOAD & EVIDENCE SYSTEM =====

function initializeFileUpload() {
    const fileUpload = document.getElementById('fileUpload');
    const uploadBtn = document.getElementById('uploadBtn');
    const evidenceArea = document.getElementById('evidenceArea');

    if (uploadBtn) {
        uploadBtn.addEventListener('click', () => fileUpload.click());
    }

    if (fileUpload) {
        fileUpload.addEventListener('change', handleFileUpload);
    }

    if (evidenceArea) {
        // Drag and drop functionality
        evidenceArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            evidenceArea.classList.add('drag-over');
        });

        evidenceArea.addEventListener('dragleave', () => {
            evidenceArea.classList.remove('drag-over');
        });

        evidenceArea.addEventListener('drop', (e) => {
            e.preventDefault();
            evidenceArea.classList.remove('drag-over');
            handleFileUpload({ target: { files: e.dataTransfer.files } });
        });
    }
}

function handleFileUpload(event) {
    const files = Array.from(event.target.files);
    const uploadedFiles = document.getElementById('uploadedFiles');

    files.forEach(file => {
        const fileItem = document.createElement('div');
        fileItem.className = 'uploaded-file-item d-flex justify-content-between align-items-center p-2 border rounded mb-2';

        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <i class="bi bi-file-earmark"></i>
            <span class="ms-2">${file.name}</span>
            <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
        `;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.addEventListener('click', () => fileItem.remove());

        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeBtn);
        uploadedFiles.appendChild(fileItem);

        // Process image files for preview
        if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxWidth = '100px';
                preview.style.maxHeight = '100px';
                fileInfo.appendChild(preview);
            };
            reader.readAsDataURL(file);
        }
    });

    showToast(`${files.length} file(s) uploaded successfully!`, 'success');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Screenshot tools removed

// Show toast notification
function showToast(message, type) {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');

    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    toastContainer.appendChild(toastEl);

    // Initialize and show toast
    const toast = new bootstrap.Toast(toastEl, { delay: 3000 });
    toast.show();

    // Remove toast after it's hidden
    toastEl.addEventListener('hidden.bs.toast', function() {
        toastEl.remove();
    });
}
// ===== ANALYTICS SYSTEM =====

function initializeAnalytics() {
    const analyticsBtn = document.getElementById('analyticsBtn');

    if (analyticsBtn) {
        analyticsBtn.addEventListener('click', () => {
            const modal = new bootstrap.Modal(document.getElementById('analyticsModal'));
            modal.show();
            generateAnalytics();
        });
    }
}

function updateAnalytics() {
    const bugs = getBugsFromStorage();

    // Update summary stats
    document.getElementById('totalBugs').textContent = bugs.length;
    document.getElementById('criticalBugs').textContent = bugs.filter(b => b.severity === 'Critical').length;
    document.getElementById('highBugs').textContent = bugs.filter(b => b.severity === 'High').length;

    // Calculate average quality
    const avgQuality = bugs.length > 0 ?
        Math.round(bugs.reduce((sum, bug) => sum + (bug.qualityScore || 0), 0) / bugs.length) : 0;
    document.getElementById('avgQuality').textContent = avgQuality + '%';

    // Calculate weekly bugs
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
    const weeklyBugs = bugs.filter(bug => new Date(bug.dateCreated) > oneWeekAgo).length;
    document.getElementById('weeklyBugs').textContent = weeklyBugs;
}

function generateAnalytics() {
    const bugs = getBugsFromStorage();

    // Generate severity chart
    generateSeverityChart(bugs);

    // Generate category chart
    generateCategoryChart(bugs);

    // Generate trends chart
    generateTrendsChart(bugs);

    // Generate quality metrics
    generateQualityMetrics(bugs);

    // Generate hotspots
    generateBugHotspots(bugs);
}

function generateSeverityChart(bugs) {
    const ctx = document.getElementById('severityChart').getContext('2d');

    const severityCounts = {
        'Critical': bugs.filter(b => b.severity === 'Critical').length,
        'High': bugs.filter(b => b.severity === 'High').length,
        'Medium': bugs.filter(b => b.severity === 'Medium').length,
        'Low': bugs.filter(b => b.severity === 'Low').length
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(severityCounts),
            datasets: [{
                data: Object.values(severityCounts),
                backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#28a745']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function generateCategoryChart(bugs) {
    const ctx = document.getElementById('categoryChart').getContext('2d');

    const categoryCounts = {};
    bugs.forEach(bug => {
        const category = bug.category || 'Uncategorized';
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
    });

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: Object.keys(categoryCounts),
            datasets: [{
                label: 'Bug Count',
                data: Object.values(categoryCounts),
                backgroundColor: '#17a2b8'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function generateTrendsChart(bugs) {
    const ctx = document.getElementById('trendsChart').getContext('2d');

    // Group bugs by week
    const weeklyData = {};
    bugs.forEach(bug => {
        const date = new Date(bug.dateCreated);
        const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
        const weekKey = weekStart.toISOString().split('T')[0];
        weeklyData[weekKey] = (weeklyData[weekKey] || 0) + 1;
    });

    const sortedWeeks = Object.keys(weeklyData).sort();

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: sortedWeeks.map(week => new Date(week).toLocaleDateString()),
            datasets: [{
                label: 'Bugs Reported',
                data: sortedWeeks.map(week => weeklyData[week]),
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function generateQualityMetrics(bugs) {
    const qualityMetrics = document.getElementById('qualityMetrics');

    const avgQuality = bugs.length > 0 ?
        bugs.reduce((sum, bug) => sum + (bug.qualityScore || 0), 0) / bugs.length : 0;

    const highQualityBugs = bugs.filter(bug => (bug.qualityScore || 0) >= 80).length;
    const lowQualityBugs = bugs.filter(bug => (bug.qualityScore || 0) < 50).length;

    qualityMetrics.innerHTML = `
        <div class="row text-center">
            <div class="col-4">
                <h4 class="text-primary">${Math.round(avgQuality)}%</h4>
                <small>Average Quality</small>
            </div>
            <div class="col-4">
                <h4 class="text-success">${highQualityBugs}</h4>
                <small>High Quality (80%+)</small>
            </div>
            <div class="col-4">
                <h4 class="text-danger">${lowQualityBugs}</h4>
                <small>Low Quality (<50%)</small>
            </div>
        </div>
        <div class="mt-3">
            <h6>Quality Improvement Tips:</h6>
            <ul class="small">
                <li>Add more detailed steps to reproduce</li>
                <li>Include screenshots and error messages</li>
                <li>Specify expected vs actual results clearly</li>
                <li>Provide environment information</li>
            </ul>
        </div>
    `;
}

function generateBugHotspots(bugs) {
    const hotspots = document.getElementById('bugHotspots');

    // Analyze common patterns
    const titleWords = {};
    bugs.forEach(bug => {
        const words = bug.title.toLowerCase().split(' ');
        words.forEach(word => {
            if (word.length > 3) {
                titleWords[word] = (titleWords[word] || 0) + 1;
            }
        });
    });

    const topWords = Object.entries(titleWords)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);

    const severityTrends = {
        'Critical': bugs.filter(b => b.severity === 'Critical').length,
        'High': bugs.filter(b => b.severity === 'High').length
    };

    hotspots.innerHTML = `
        <div class="mb-3">
            <h6>Common Issues:</h6>
            ${topWords.map(([word, count]) =>
                `<span class="badge bg-secondary me-1">${word} (${count})</span>`
            ).join('')}
        </div>
        <div class="mb-3">
            <h6>High Priority Issues:</h6>
            <div class="text-danger">Critical: ${severityTrends.Critical}</div>
            <div class="text-warning">High: ${severityTrends.High}</div>
        </div>
        <div>
            <h6>Recommendations:</h6>
            <ul class="small">
                ${severityTrends.Critical > 0 ? '<li class="text-danger">Address critical issues immediately</li>' : ''}
                ${topWords.length > 0 ? `<li>Focus on "${topWords[0][0]}" related issues</li>` : ''}
                <li>Implement preventive measures for common patterns</li>
            </ul>
        </div>
    `;
}

// ===== SEARCH AND FILTER SYSTEM =====

function initializeSearchAndFilter() {
    const searchInput = document.getElementById('searchBugs');
    const severityFilter = document.getElementById('filterSeverity');
    const categoryFilter = document.getElementById('filterCategory');

    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterBugs, 300));
    }

    if (severityFilter) {
        severityFilter.addEventListener('change', filterBugs);
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterBugs);
    }
}

function filterBugs() {
    const searchTerm = document.getElementById('searchBugs').value.toLowerCase();
    const severityFilter = document.getElementById('filterSeverity').value;
    const categoryFilter = document.getElementById('filterCategory').value;

    const bugs = getBugsFromStorage();

    const filteredBugs = bugs.filter(bug => {
        const matchesSearch = !searchTerm ||
            bug.title.toLowerCase().includes(searchTerm) ||
            bug.description.toLowerCase().includes(searchTerm);

        const matchesSeverity = !severityFilter || bug.severity === severityFilter;
        const matchesCategory = !categoryFilter || bug.category === categoryFilter;

        return matchesSearch && matchesSeverity && matchesCategory;
    });

    displayFilteredBugs(filteredBugs);
}

function displayFilteredBugs(bugs) {
    const bugList = document.getElementById('bugList');
    const noBugsMessage = document.getElementById('noBugsMessage');

    bugList.innerHTML = '';

    if (bugs.length === 0) {
        noBugsMessage.classList.remove('d-none');
        noBugsMessage.innerHTML = '<p class="lead">No bugs match your filters. Try adjusting your search criteria.</p>';
    } else {
        noBugsMessage.classList.add('d-none');
        bugs.forEach(bug => {
            const row = createBugRow(bug);
            bugList.appendChild(row);
        });
    }
    // Re-initialize copy buttons for any dynamically added content
    setTimeout(() => {
        initializeCopyButtons();
    }, 100);
}

// ===== EXPORT SYSTEM =====

function initializeExports() {
    // Export modal button
    const integrationBtn = document.getElementById('integrationBtn');
    if (integrationBtn) {
        integrationBtn.addEventListener('click', () => {
            const modal = new bootstrap.Modal(document.getElementById('integrationModal'));
            modal.show();
        });
    }

    // Export buttons
    const exportJsonBtn = document.getElementById('exportJsonBtn');
    const exportCsvBtn = document.getElementById('exportCsvBtn');
    const exportPdfBtn = document.getElementById('exportPdfBtn');
    const exportEmailBtn = document.getElementById('exportEmailBtn');

    if (exportJsonBtn) {
        exportJsonBtn.addEventListener('click', exportAsJson);
    }

    if (exportCsvBtn) {
        exportCsvBtn.addEventListener('click', exportAsCsv);
    }

    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', exportAsPdf);
    }

    if (exportEmailBtn) {
        exportEmailBtn.addEventListener('click', exportAsEmail);
    }
}

function exportAsCsv() {
    const bugs = getBugsFromStorage();

    const csvContent = [
        ['ID', 'Title', 'Description', 'Severity', 'Category', 'Steps', 'Actual Result', 'Expected Result', 'Date Created', 'Quality Score'],
        ...bugs.map(bug => [
            bug.id,
            bug.title,
            bug.description,
            bug.severity,
            bug.category || '',
            bug.stepsToReproduce,
            bug.actualResult,
            bug.expectedResult,
            new Date(bug.dateCreated).toLocaleDateString(),
            bug.qualityScore || 0
        ])
    ].map(row => row.map(field => `"${field}"`).join(',')).join('\n');

    downloadFile(csvContent, 'bugs-export.csv', 'text/csv');
    showToast('Bugs exported as CSV!', 'success');
}

function exportAsPdf() {
    const bugs = getBugsFromStorage();

    // Create a simple HTML report
    const htmlContent = `
        <html>
        <head>
            <title>Bug Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .bug { border: 1px solid #ddd; margin: 10px 0; padding: 15px; }
                .severity-critical { border-left: 5px solid #dc3545; }
                .severity-high { border-left: 5px solid #fd7e14; }
                .severity-medium { border-left: 5px solid #ffc107; }
                .severity-low { border-left: 5px solid #28a745; }
                h1 { color: #333; }
                h3 { color: #666; margin-bottom: 5px; }
                .meta { color: #888; font-size: 0.9em; }
            </style>
        </head>
        <body>
            <h1>Bug Report - Generated ${new Date().toLocaleDateString()}</h1>
            <p>Total Bugs: ${bugs.length}</p>
            ${bugs.map(bug => `
                <div class="bug severity-${bug.severity.toLowerCase()}">
                    <h3>${bug.title}</h3>
                    <div class="meta">Severity: ${bug.severity} | Date: ${new Date(bug.dateCreated).toLocaleDateString()}</div>
                    <p><strong>Description:</strong> ${bug.description}</p>
                    <p><strong>Steps to Reproduce:</strong> ${bug.stepsToReproduce}</p>
                    <p><strong>Actual Result:</strong> ${bug.actualResult}</p>
                    <p><strong>Expected Result:</strong> ${bug.expectedResult}</p>
                </div>
            `).join('')}
        </body>
        </html>
    `;

    // Open in new window for printing/saving as PDF using modern approach
    const newWindow = window.open('', '_blank');
    if (newWindow) {
        try {
            // Use modern approach with innerHTML
            newWindow.document.documentElement.innerHTML = htmlContent;
            newWindow.document.close();
            newWindow.print();
        } catch (error) {
            console.warn('Modern approach failed, using fallback:', error);
            // Fallback to document.write with error handling
            try {
                newWindow.document.write(htmlContent);
                newWindow.document.close();
                newWindow.print();
            } catch (fallbackError) {
                console.error('PDF generation failed:', fallbackError);
                showToast('Failed to generate PDF. Please try again.', 'error');
                newWindow.close();
                return;
            }
        }
    } else {
        showToast('Failed to open new window. Please check popup blocker.', 'error');
        return;
    }

    showToast('PDF report opened in new window!', 'success');
}

function exportAsEmail() {
    const bugs = getBugsFromStorage();
    const criticalBugs = bugs.filter(b => b.severity === 'Critical');
    const highBugs = bugs.filter(b => b.severity === 'High');

    const subject = `Bug Report Summary - ${bugs.length} Total Issues`;
    const body = `
Bug Report Summary
==================

Total Bugs: ${bugs.length}
Critical: ${criticalBugs.length}
High: ${highBugs.length}

Critical Issues:
${criticalBugs.map(bug => `- ${bug.title}`).join('\n')}

High Priority Issues:
${highBugs.map(bug => `- ${bug.title}`).join('\n')}

Generated on: ${new Date().toLocaleDateString()}
    `;

    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;

    showToast('Email client opened with bug report!', 'success');
}











function downloadFile(content, filename, contentType) {
    const blob = new Blob([content], { type: contentType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}






// ===== HELPER FUNCTIONS =====

function calculateBugQualityScore(title, description, severity, steps, actualResult, expectedResult) {
    let score = 0;
    let bonusPoints = 0;

    // Enhanced Title scoring (25 points)
    if (title) {
        const titleLength = title.length;
        const titleWords = title.split(' ').length;

        if (titleLength >= 15 && titleLength <= 80 && titleWords >= 3) {
            score += 25;
            // Bonus for descriptive titles
            if (title.toLowerCase().includes('error') || title.toLowerCase().includes('issue') ||
                title.toLowerCase().includes('bug') || title.toLowerCase().includes('problem')) {
                bonusPoints += 2;
            }
        } else if (titleLength >= 10 && titleWords >= 2) {
            score += 18;
        } else if (titleLength > 0) {
            score += 8;
        }
    }

    // Enhanced Description scoring (30 points)
    if (description) {
        const descLength = description.length;
        const sentences = description.split(/[.!?]+/).filter(s => s.trim().length > 0).length;

        if (descLength >= 100 && sentences >= 3) {
            score += 30;
            // Bonus for technical details
            const technicalTerms = ['error', 'exception', 'browser', 'version', 'steps', 'reproduce'];
            const techTermCount = technicalTerms.filter(term =>
                description.toLowerCase().includes(term)).length;
            bonusPoints += Math.min(techTermCount * 1, 5);
        } else if (descLength >= 50 && sentences >= 2) {
            score += 22;
        } else if (descLength >= 20) {
            score += 12;
        } else if (descLength > 0) {
            score += 6;
        }
    }

    // Enhanced Severity scoring (15 points)
    if (severity) {
        score += 15;
        // Bonus for appropriate severity selection
        const titleDesc = (title + ' ' + description).toLowerCase();
        if ((severity === 'Critical' && (titleDesc.includes('crash') || titleDesc.includes('data loss'))) ||
            (severity === 'High' && titleDesc.includes('error')) ||
            (severity === 'Low' && (titleDesc.includes('cosmetic') || titleDesc.includes('minor')))) {
            bonusPoints += 2;
        }
    }

    // Enhanced Steps scoring (20 points)
    if (steps) {
        const stepsLength = steps.length;
        const stepCount = steps.split(/\n|\d+\.|step/i).filter(s => s.trim().length > 5).length;

        if (stepsLength >= 50 && stepCount >= 3) {
            score += 20;
            // Bonus for numbered or structured steps
            if (/\d+\.|step \d+/i.test(steps)) {
                bonusPoints += 3;
            }
        } else if (stepsLength >= 30 && stepCount >= 2) {
            score += 15;
        } else if (stepsLength >= 10) {
            score += 8;
        } else if (stepsLength > 0) {
            score += 4;
        }
    }

    // Enhanced Actual result scoring (10 points)
    if (actualResult) {
        const actualLength = actualResult.length;
        if (actualLength >= 20) {
            score += 10;
            // Bonus for specific error messages or details
            if (actualResult.includes('error') || actualResult.includes('message') ||
                actualResult.includes('code')) {
                bonusPoints += 1;
            }
        } else if (actualLength >= 10) {
            score += 7;
        } else if (actualLength > 0) {
            score += 3;
        }
    }

    // Enhanced Expected result scoring (10 points)
    if (expectedResult) {
        const expectedLength = expectedResult.length;
        if (expectedLength >= 20) {
            score += 10;
            // Bonus for clear expectations
            if (expectedResult.includes('should') || expectedResult.includes('expected') ||
                expectedResult.includes('correct')) {
                bonusPoints += 1;
            }
        } else if (expectedLength >= 10) {
            score += 7;
        } else if (expectedLength > 0) {
            score += 3;
        }
    }

    // Apply bonus points (max 10 bonus points)
    const finalScore = Math.min(score + Math.min(bonusPoints, 10), 100);

    return finalScore;
}

function detectBugCategoryFromContent(content) {
    const categories = {
        'UI/UX': {
            keywords: ['button', 'layout', 'design', 'interface', 'display', 'visual', 'alignment', 'color', 'font', 'css', 'style', 'responsive', 'mobile view'],
            weight: 1.0
        },
        'Performance': {
            keywords: ['slow', 'lag', 'freeze', 'timeout', 'loading', 'speed', 'memory', 'cpu', 'optimization', 'delay', 'hang'],
            weight: 1.2
        },
        'Security': {
            keywords: ['login', 'password', 'auth', 'permission', 'access', 'security', 'vulnerability', 'token', 'encryption', 'unauthorized'],
            weight: 1.5
        },
        'Functional': {
            keywords: ['feature', 'function', 'work', 'broken', 'not working', 'fails', 'error', 'malfunction', 'defect', 'bug'],
            weight: 1.1
        },
        'Integration': {
            keywords: ['api', 'service', 'connection', 'sync', 'import', 'export', 'third-party', 'webhook', 'external', 'plugin'],
            weight: 1.2
        },
        'Data': {
            keywords: ['database', 'data', 'save', 'load', 'corrupt', 'missing', 'duplicate', 'backup', 'restore', 'sync'],
            weight: 1.3
        },
        'Mobile': {
            keywords: ['mobile', 'phone', 'tablet', 'touch', 'swipe', 'responsive', 'ios', 'android', 'device'],
            weight: 1.1
        },
        'Browser': {
            keywords: ['chrome', 'firefox', 'safari', 'edge', 'browser', 'compatibility', 'cross-browser'],
            weight: 1.1
        },
        'Network': {
            keywords: ['network', 'connection', 'internet', 'offline', 'server', 'connectivity', 'request failed'],
            weight: 1.2
        },
        'Validation': {
            keywords: ['validation', 'form', 'input', 'required', 'format', 'email', 'phone', 'field'],
            weight: 1.0
        }
    };

    const detectedCategories = [];
    const contentLower = content.toLowerCase();

    for (const [category, config] of Object.entries(categories)) {
        let score = 0;
        let matchCount = 0;

        for (const keyword of config.keywords) {
            if (contentLower.includes(keyword)) {
                matchCount++;
                // Longer keywords get more weight
                const keywordWeight = keyword.length > 5 ? 2 : 1;
                score += keywordWeight * config.weight;
            }
        }

        // Bonus for multiple matches
        if (matchCount > 1) {
            score += matchCount * 0.3;
        }

        if (score > 0) {
            // Calculate confidence based on score and match count
            const confidence = Math.min(0.95, 0.5 + (score / 10) + (matchCount * 0.1));
            detectedCategories.push({ category, confidence, score, matchCount });
        }
    }

    // Sort by score and return top categories
    return detectedCategories.sort((a, b) => b.score - a.score);
}

function getUploadedFiles() {
    const uploadedFiles = document.getElementById('uploadedFiles');
    const files = [];

    if (uploadedFiles) {
        const fileItems = uploadedFiles.querySelectorAll('.uploaded-file-item');
        fileItems.forEach(item => {
            const fileName = item.querySelector('span').textContent;
            files.push({ name: fileName, type: 'file' });
        });
    }

    return files;
}

function clearUploadedFiles() {
    const uploadedFiles = document.getElementById('uploadedFiles');
    if (uploadedFiles) {
        uploadedFiles.innerHTML = '';
    }
}

// Update createBugRow to include new columns
function createBugRow(bug) {
    const row = document.createElement('tr');
    row.setAttribute('data-id', bug.id);

    const dateCreated = new Date(bug.dateCreated).toLocaleDateString();
    const severityClass = `badge-${bug.severity.toLowerCase()}`;

    // Quality score with color coding
    const qualityScore = bug.qualityScore || 0;
    let qualityClass = 'text-danger';
    if (qualityScore >= 80) qualityClass = 'text-success';
    else if (qualityScore >= 60) qualityClass = 'text-warning';

    // Category badge
    const category = bug.category || 'Uncategorized';

    row.innerHTML = `
        <td>
            <a href="#" class="bug-title-link" onclick="viewBugDetails('${bug.id}')">
                ${bug.title}
            </a>
        </td>
        <td>
            <span class="badge bg-secondary">${category}</span>
        </td>
        <td>
            <span class="badge ${severityClass}">${bug.severity}</span>
        </td>
        <td>
            <span class="${qualityClass} fw-bold">${qualityScore}%</span>
        </td>
        <td>
            <small>${dateCreated}</small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="viewBugDetails('${bug.id}')" title="View Details">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="editBug('${bug.id}')" title="Edit">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="printBug('${bug.id}')" title="Print Bug">
                    <i class="bi bi-printer"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteBug('${bug.id}')" title="Delete">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}



// Add missing exportAsJson function
function exportAsJson() {
    const bugs = getBugsFromStorage();

    if (bugs.length === 0) {
        showToast('No bugs to export', 'warning');
        return;
    }

    const dataStr = JSON.stringify(bugs, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

    const exportFileDefaultName = 'bug-report-' + new Date().toISOString().slice(0, 10) + '.json';

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();

    showToast('Bugs exported as JSON!', 'success');
}

// Ensure footer is visible
document.addEventListener('DOMContentLoaded', function() {
    // Make sure footer is visible
    const footer = document.getElementById('appFooter');
    if (footer) {
        footer.style.display = 'block';
        footer.style.visibility = 'visible';
        footer.style.opacity = '1';

        // Also ensure all children are visible
        const footerElements = footer.querySelectorAll('*');
        footerElements.forEach(element => {
            element.style.display = 'block';
            element.style.visibility = 'visible';
            element.style.opacity = '1';
        });

        console.log('Footer visibility enforced');
    }
});