<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Bug Analyzer & Test Generator - Intelligent Bug Reporting Tool</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="favicon.svg">
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="shortcut icon" href="favicon.ico">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Chart.js for Analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container mt-4">
        <header class="text-center mb-5" data-aos="fade-down">
            <h1 class="display-4">AI Bug Analyzer & Test Generator</h1>
            <p class="lead">Transform bug reports into intelligent insights and comprehensive test cases with AI-powered analysis</p>

            <!-- Smart Features Toolbar -->
            <div class="smart-toolbar mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="row justify-content-center">
                    <!-- Voice input button removed -->
                    <div class="col-auto">
                        <button class="btn btn-outline-success btn-sm" id="templateWizardBtn" title="Template Wizard">
                            <i class="bi bi-magic"></i> Wizard
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-info btn-sm" id="analyticsBtn" title="Bug Analytics">
                            <i class="bi bi-graph-up"></i> Analytics
                        </button>
                    </div>
                    <!-- Screenshot button removed -->
                    <div class="col-auto">
                        <button class="btn btn-outline-secondary btn-sm" id="integrationBtn" title="Export Options">
                            <i class="bi bi-download"></i> Export
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-info btn-sm" onclick="showModelComparison()" title="Compare AI Models">
                            <i class="bi bi-graph-up"></i> Compare AI
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-dark btn-sm" onclick="analyticsManager.generateDashboard()" title="Analytics Dashboard">
                            <i class="bi bi-bar-chart"></i> Analytics
                        </button>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#settingsModal" title="AI Settings">
                            <i class="bi bi-gear"></i> AI Settings
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="row">
            <!-- Bug Form -->
            <div class="col-lg-4 mb-4" data-aos="fade-right">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">Report a Bug</h3>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" id="templateDropdown" data-bs-toggle="dropdown">
                                <i class="bi bi-file-earmark-text"></i> Template
                            </button>
                            <ul class="dropdown-menu" id="templateMenu">
                                <li><a class="dropdown-item" href="#" data-template="general">General Bug</a></li>
                                <li><a class="dropdown-item" href="#" data-template="ui">UI/UX Issue</a></li>
                                <li><a class="dropdown-item" href="#" data-template="performance">Performance</a></li>
                                <li><a class="dropdown-item" href="#" data-template="security">Security</a></li>
                                <li><a class="dropdown-item" href="#" data-template="mobile">Mobile</a></li>
                                <li><a class="dropdown-item" href="#" data-template="integration">Integration</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="bugForm">
                            <input type="hidden" id="bugId">
                            <!-- Bug Quality Score Card -->
                            <div class="mb-3" id="qualityScoreCard" style="display: none;">
                                <div class="card border-info">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">Bug Report Quality</h6>
                                            <span class="badge bg-info" id="qualityScore">0%</span>
                                        </div>
                                        <div class="progress mb-2" style="height: 8px;">
                                            <div class="progress-bar bg-info" id="qualityProgress" role="progressbar" style="width: 0%"></div>
                                        </div>
                                        <div id="qualityFeedback" class="small text-muted"></div>
                                        <div id="missingSuggestions" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="title" class="form-label">Title</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="title" required placeholder="Describe the issue briefly...">
                                    <button class="btn btn-outline-secondary btn-sm copy-btn" type="button" data-target="title" title="Copy Title"><i class="bi bi-clipboard"></i></button>
                                    <button class="btn btn-outline-info" type="button" id="aiEnhanceBtn" title="AI Enhance">
                                        <i class="bi bi-robot"></i>
                                    </button>
                                </div>
                                <div id="titleSuggestions" class="mt-1"></div>
                                <div id="duplicateWarning" class="mt-1"></div>
                            </div>
                            <div class="mt-2 d-grid">
                                <button type="button" class="btn btn-info" id="generateBtn">Generate</button>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="description" class="form-label mb-0">Description</label>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="description" title="Copy Description"><i class="bi bi-clipboard"></i></button>
                                </div>
                                <textarea class="form-control mt-1" id="description" rows="3"></textarea>
                                <div id="descriptionSuggestions" class="mt-1"></div>
                            </div>

                            <!-- Auto-detected Bug Category -->
                            <div class="mb-3" id="categorySection" style="display: none;">
                                <label class="form-label">Auto-detected Category</label>
                                <div id="bugCategory" class="d-flex flex-wrap gap-1"></div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="severity" class="form-label mb-0">Severity</label>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="severity" title="Copy Severity"><i class="bi bi-clipboard"></i></button>
                                </div>
                                <select class="form-select mt-1" id="severity">
                                    <option value="">Select severity</option>
                                    <option value="Low">Low</option>
                                    <option value="Medium">Medium</option>
                                    <option value="High">High</option>
                                    <option value="Critical">Critical</option>
                                </select>
                                <div id="severitySuggestion" class="mt-1"></div>
                            </div>

                            <!-- Preconditions section removed -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="stepsToReproduce" class="form-label mb-0">Steps to Reproduce</label>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="stepsToReproduce" title="Copy Steps to Reproduce"><i class="bi bi-clipboard"></i></button>
                                </div>
                                <textarea class="form-control mt-1" id="stepsToReproduce" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="actualResult" class="form-label mb-0">Actual Result</label>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="actualResult" title="Copy Actual Result"><i class="bi bi-clipboard"></i></button>
                                </div>
                                <textarea class="form-control mt-1" id="actualResult" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="expectedResult" class="form-label mb-0">Expected Result</label>
                                    <button class="btn btn-sm btn-outline-secondary copy-btn" type="button" data-target="expectedResult" title="Copy Expected Result"><i class="bi bi-clipboard"></i></button>
                                </div>
                                <textarea class="form-control mt-1" id="expectedResult" rows="2"></textarea>
                            </div>

                            <!-- Environment Detection -->
                            <div class="mb-3" id="environmentSection">
                                <label class="form-label">Environment <span class="badge bg-success">Auto-detected</span></label>
                                <div class="row gy-2">
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="bi bi-browser-chrome"></i></span>
                                            <input type="text" class="form-control" id="browserInfo" placeholder="Browser" readonly>
                                            <button class="btn btn-outline-secondary copy-btn" type="button" data-target="browserInfo" title="Copy Browser Info"><i class="bi bi-clipboard"></i></button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="bi bi-pc-display"></i></span>
                                            <input type="text" class="form-control" id="osInfo" placeholder="Operating System" readonly>
                                            <button class="btn btn-outline-secondary copy-btn" type="button" data-target="osInfo" title="Copy OS Info"><i class="bi bi-clipboard"></i></button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="bi bi-aspect-ratio"></i></span>
                                            <input type="text" class="form-control" id="screenResolution" placeholder="Screen Resolution" readonly>
                                            <button class="btn btn-outline-secondary copy-btn" type="button" data-target="screenResolution" title="Copy Screen Resolution"><i class="bi bi-clipboard"></i></button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="bi bi-file-earmark-code"></i></span>
                                            <input type="text" class="form-control" id="userAgent" placeholder="User Agent" readonly>
                                            <button class="btn btn-outline-secondary copy-btn" type="button" data-target="userAgent" title="Copy User Agent"><i class="bi bi-clipboard"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Evidence & Attachments section removed -->

                            <!-- AI Insights Panel -->
                            <div class="mb-3" id="aiInsightsPanel" style="display: none;">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white py-2">
                                        <h6 class="mb-0"><i class="bi bi-lightbulb"></i> AI Insights</h6>
                                    </div>
                                    <div class="card-body p-3">
                                        <div id="aiInsights"></div>
                                        <div id="relatedBugs" class="mt-2"></div>
                                        <div id="testCaseSuggestions" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary" id="submitBug">Submit Bug</button>
                                <button type="button" class="btn btn-secondary d-none" id="cancelEdit">Cancel Edit</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="mt-3 d-grid">
                    <button class="btn btn-success" id="exportBtn">Export as JSON</button>
                </div>
            </div>

            <!-- Bug List -->
            <div class="col-lg-8" data-aos="fade-left">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h3 class="card-title mb-0">Bug List</h3>
                        </div>

                        <!-- Analytics Summary -->
                        <div class="row text-center" id="bugAnalytics">
                            <div class="col">
                                <div class="small">Total Bugs</div>
                                <div class="fw-bold" id="totalBugs">0</div>
                            </div>
                            <div class="col">
                                <div class="small">Critical</div>
                                <div class="fw-bold text-danger" id="criticalBugs">0</div>
                            </div>
                            <div class="col">
                                <div class="small">High</div>
                                <div class="fw-bold text-warning" id="highBugs">0</div>
                            </div>
                            <div class="col">
                                <div class="small">Quality Avg</div>
                                <div class="fw-bold text-info" id="avgQuality">0%</div>
                            </div>
                            <div class="col">
                                <div class="small">This Week</div>
                                <div class="fw-bold text-success" id="weeklyBugs">0</div>
                            </div>
                        </div>

                        <!-- Filters and Search -->
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="searchBugs" placeholder="Search bugs...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filterSeverity">
                                    <option value="">All Severities</option>
                                    <option value="Critical">Critical</option>
                                    <option value="High">High</option>
                                    <option value="Medium">Medium</option>
                                    <option value="Low">Low</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="filterCategory">
                                    <option value="">All Categories</option>
                                    <option value="UI/UX">UI/UX</option>
                                    <option value="Performance">Performance</option>
                                    <option value="Security">Security</option>
                                    <option value="Functional">Functional</option>
                                    <option value="Integration">Integration</option>
                                    <option value="Mobile">Mobile</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Severity</th>
                                        <th>Quality</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="bugList">
                                    <!-- Bug items will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                        <div id="noBugsMessage" class="text-center py-4 d-none">
                            <p class="lead">No bugs reported yet. Add your first bug!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Attribution text with LinkedIn -->
        <div class="d-flex justify-content-center align-items-center flex-column py-3 mt-5" data-aos="fade-up" data-aos-duration="800">
            <p class="mb-1 fw-bold">MADE WITH <span class="text-danger">❤️</span> BY AHMED GAMAL</p>
            <div class="d-flex align-items-center gap-2 mb-2">
                <p class="text-muted small mb-0">Version 1.0 - 2025</p>
                <span class="text-muted">•</span>
                <a href="https://www.linkedin.com/in/ahmed-gamal-682757336?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app" target="_blank" rel="noopener noreferrer"
                   class="text-decoration-none d-flex align-items-center gap-1"
                   title="Connect on LinkedIn">
                    <i class="bi bi-linkedin text-primary"></i>
                    <span class="text-muted small">LinkedIn</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Template Wizard Modal -->
    <div class="modal fade" id="templateWizardModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title"><i class="bi bi-magic"></i> Smart Template Wizard</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="wizardContent">
                        <div class="text-center">
                            <h6>What type of issue are you reporting?</h6>
                            <div class="row mt-3">
                                <div class="col-md-4 mb-3">
                                    <div class="card template-card" data-template="ui">
                                        <div class="card-body text-center">
                                            <i class="bi bi-palette fs-1 text-primary"></i>
                                            <h6>UI/UX Issue</h6>
                                            <p class="small">Visual problems, layout issues, design inconsistencies</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card template-card" data-template="performance">
                                        <div class="card-body text-center">
                                            <i class="bi bi-speedometer2 fs-1 text-warning"></i>
                                            <h6>Performance</h6>
                                            <p class="small">Slow loading, timeouts, memory issues</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="card template-card" data-template="security">
                                        <div class="card-body text-center">
                                            <i class="bi bi-shield-exclamation fs-1 text-danger"></i>
                                            <h6>Security</h6>
                                            <p class="small">Authentication, permissions, vulnerabilities</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics Modal -->
    <div class="modal fade" id="analyticsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title"><i class="bi bi-graph-up"></i> Bug Analytics Dashboard</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Severity Distribution</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="severityChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Category Breakdown</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="categoryChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Bug Trends Over Time</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="trendsChart" width="800" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Quality Metrics</h6>
                                </div>
                                <div class="card-body">
                                    <div id="qualityMetrics"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Hotspots & Patterns</h6>
                                </div>
                                <div class="card-body">
                                    <div id="bugHotspots"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="integrationModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-secondary text-white">
                    <h5 class="modal-title"><i class="bi bi-download"></i> Export Options</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Export Options</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success" id="exportJsonBtn">
                                    <i class="bi bi-filetype-json"></i> Export as JSON
                                </button>
                                <button class="btn btn-outline-primary" id="exportCsvBtn">
                                    <i class="bi bi-filetype-csv"></i> Export as CSV
                                </button>
                                <button class="btn btn-outline-danger" id="exportPdfBtn">
                                    <i class="bi bi-filetype-pdf"></i> Export as PDF Report
                                </button>
                                <button class="btn btn-outline-warning" id="exportEmailBtn">
                                    <i class="bi bi-envelope"></i> Email Report
                                </button>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Voice Input Modal removed -->

    <!-- Screenshot Tool Modal removed -->

    <!-- Bug Details Modal -->
    <div class="modal fade" id="bugDetailsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Bug Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="bugDetailsContent">
                    <!-- Bug details will be added here dynamically -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="bi bi-robot"></i> AI & Multi-API Settings</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="mb-3">
                            <label for="apiKey" class="form-label">Google Gemini API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="apiKey" placeholder="Enter your Google Gemini API key">
                                <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">Your API key is stored locally and never sent to our servers.</div>
                        </div>


                        <!-- Multi-API Settings -->
                        <div class="mb-3">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="useMultiAPI" checked>
                                <label class="form-check-label" for="useMultiAPI">
                                    <strong>Enable Multi-AI System</strong>
                                    <small class="d-block text-muted">Use multiple AI models for enhanced accuracy and reliability</small>
                                </label>
                            </div>

                            <!-- API Status Display -->
                            <div class="card border-info mb-3" id="apiStatusCard">
                                <div class="card-header bg-info text-white py-2">
                                    <h6 class="mb-0"><i class="bi bi-robot"></i> AI Models Status</h6>
                                </div>
                                <div class="card-body p-3">
                                    <div id="apiStatus" class="mb-2">
                                        <div class="d-flex align-items-center gap-2">
                                            <i class="bi bi-robot text-success"></i>
                                            <span class="badge bg-success">3/3 APIs Active</span>
                                        </div>
                                    </div>
                                    <div class="row" id="apiHealthStatus">
                                        <!-- API health status will be populated here -->
                                    </div>
                                    <div class="d-flex gap-2 mt-2">
                                        <button type="button" class="btn btn-sm btn-outline-info" id="refreshAPIStatus">
                                            <i class="bi bi-arrow-clockwise"></i> Refresh Status
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="runPerformanceBenchmark()">
                                            <i class="bi bi-speedometer2"></i> Run Benchmark
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced API Settings -->
                            <div class="accordion" id="advancedAPISettings">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#apiConfigCollapse">
                                            <i class="bi bi-gear"></i>&nbsp; Advanced API Configuration
                                        </button>
                                    </h2>
                                    <div id="apiConfigCollapse" class="accordion-collapse collapse" data-bs-parent="#advancedAPISettings">
                                        <div class="accordion-body">
                                            <div class="alert alert-warning alert-sm">
                                                <i class="bi bi-exclamation-triangle"></i>
                                                <strong>Note:</strong> API keys are pre-configured for optimal performance. Changes are automatically saved.
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Task Optimization</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="enableTaskOptimization" checked>
                                                    <label class="form-check-label" for="enableTaskOptimization">
                                                        Automatically select best AI model for each task
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Load Balancing</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="enableLoadBalancing" checked>
                                                    <label class="form-check-label" for="enableLoadBalancing">
                                                        Distribute requests across multiple APIs
                                                    </label>
                                                </div>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Fallback Strategy</label>
                                                <select class="form-select" id="fallbackStrategy">
                                                    <option value="intelligent">Intelligent Fallback (Recommended)</option>
                                                    <option value="sequential">Sequential Fallback</option>
                                                    <option value="performance">Performance-Based</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveSettings">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- AI Bug Intelligence Integration -->
    <script src="ai_bug_system/integration.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
</body>
</html>